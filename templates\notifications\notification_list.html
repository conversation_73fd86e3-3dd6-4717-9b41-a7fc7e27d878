{% extends 'base.html' %}
{% load static %}

{% block title %}الإشعارات{% endblock %}

{% block extra_css %}
<style>
    .notification-item {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .notification-item.unread {
        background-color: #f8f9fa;
        border-left-color: #007bff;
    }
    
    .notification-item:hover {
        background-color: #e9ecef;
        transform: translateX(-2px);
    }
    
    .notification-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-time {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .notification-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .notification-message {
        color: #495057;
        line-height: 1.4;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bell me-2"></i>جميع الإشعارات
                    </h6>
                    <div>
                        {% if notifications %}
                        <button class="btn btn-outline-primary btn-sm" id="markAllAsReadBtn">
                            <i class="fas fa-check-double me-1"></i>
                            تحديد الكل كمقروء
                        </button>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="notification-item p-3 border-bottom {% if not notification.is_read %}unread{% endif %}" 
                             data-notification-id="{{ notification.id }}">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon {{ notification.get_background_color }} text-white me-3">
                                    <i class="fas {{ notification.icon }}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <div class="notification-title">{{ notification.title }}</div>
                                        <div class="notification-time">{{ notification.get_time_since_created }}</div>
                                    </div>
                                    <div class="notification-message">{{ notification.message }}</div>
                                    {% if notification.user %}
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        إشعار شخصي
                                    </small>
                                    {% else %}
                                    <small class="text-muted">
                                        <i class="fas fa-globe me-1"></i>
                                        إشعار عام
                                    </small>
                                    {% endif %}
                                </div>
                                <div class="ms-3">
                                    {% if not notification.is_read %}
                                    <span class="badge bg-primary">جديد</span>
                                    {% else %}
                                    <span class="badge bg-secondary">مقروء</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات</h5>
                            <p class="text-muted">لم يتم العثور على أي إشعارات</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Mark all as read
    $('#markAllAsReadBtn').on('click', function() {
        $.post('{% url "notifications:mark_all_read" %}', {
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        })
        .done(function(response) {
            if (response.success) {
                $('.notification-item.unread').removeClass('unread');
                $('.badge:contains("جديد")').removeClass('bg-primary').addClass('bg-secondary').text('مقروء');
                $('#markAllAsReadBtn').hide();
                
                // Show success message
                $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                  '<i class="fas fa-check-circle me-2"></i>تم تحديد جميع الإشعارات كمقروءة' +
                  '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                  '</div>').prependTo('.container-fluid');
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء تحديث الإشعارات');
        });
    });
    
    // Mark individual notification as read on click
    $('.notification-item.unread').on('click', function() {
        var notificationId = $(this).data('notification-id');
        var $item = $(this);
        
        $.post('{% url "notifications:mark_read" notification_id=0 %}'.replace('0', notificationId), {
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        })
        .done(function(response) {
            if (response.success) {
                $item.removeClass('unread');
                $item.find('.badge:contains("جديد")').removeClass('bg-primary').addClass('bg-secondary').text('مقروء');
            }
        });
    });
});
</script>
{% endblock %}
