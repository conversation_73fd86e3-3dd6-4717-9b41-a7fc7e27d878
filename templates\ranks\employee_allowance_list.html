{% extends 'base.html' %}
{% load static %}

{% block title %}العلاوات - الرتب والعلاوات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>العلاوات</h2>
    <div>
        <a href="{% url 'ranks:employee_allowance_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة علاوات موظف
        </a>
        <a href="?export=excel{% if search_term %}&search={{ search_term }}{% endif %}{% if education_allowance %}&education_allowance={{ education_allowance }}{% endif %}{% if adjustment_allowance %}&adjustment_allowance={{ adjustment_allowance }}{% endif %}{% if transportation_allowance %}&transportation_allowance={{ transportation_allowance }}{% endif %}{% if supervisory_allowance %}&supervisory_allowance={{ supervisory_allowance }}{% endif %}{% if technical_allowance %}&technical_allowance={{ technical_allowance }}{% endif %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للرتب
        </a>
    </div>
</div>

<!-- Alert for success messages -->
<div id="alert-container"></div>

<!-- Filters Section -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <form method="get" class="row g-3">
            <!-- Search Filter -->
            <div class="col-md-3">
                <label class="form-label small">
                    <i class="fas fa-search text-primary"></i> البحث
                </label>
                <input type="text" name="search" value="{{ search_term }}" class="form-control form-control-sm" placeholder="بحث بالرقم الوزاري أو الاسم">
            </div>

            <!-- Education Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-graduation-cap text-success"></i> علاوة التعليم
                </label>
                <select name="education_allowance" class="form-select form-select-sm">
                    <option value="">الكل</option>
                    <option value="yes" {% if education_allowance == 'yes' %}selected{% endif %}>نعم</option>
                    <option value="no" {% if education_allowance == 'no' %}selected{% endif %}>لا</option>
                </select>
            </div>

            <!-- Adjustment Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-cogs text-info"></i> التجيير
                </label>
                <select name="adjustment_allowance" class="form-select form-select-sm">
                    <option value="">الكل</option>
                    <option value="yes" {% if adjustment_allowance == 'yes' %}selected{% endif %}>نعم</option>
                    <option value="no" {% if adjustment_allowance == 'no' %}selected{% endif %}>لا</option>
                </select>
            </div>

            <!-- Transportation Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-car text-warning"></i> التنقلات
                </label>
                <select name="transportation_allowance" class="form-select form-select-sm">
                    <option value="">الكل</option>
                    <option value="yes" {% if transportation_allowance == 'yes' %}selected{% endif %}>نعم</option>
                    <option value="no" {% if transportation_allowance == 'no' %}selected{% endif %}>لا</option>
                </select>
            </div>

            <!-- Supervisory Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-user-tie text-secondary"></i> العلاوة الإشرافية
                </label>
                <select name="supervisory_allowance" class="form-select form-select-sm">
                    <option value="">الكل</option>
                    <option value="yes" {% if supervisory_allowance == 'yes' %}selected{% endif %}>نعم</option>
                    <option value="no" {% if supervisory_allowance == 'no' %}selected{% endif %}>لا</option>
                </select>
            </div>

            <!-- Submit Button -->
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary btn-sm w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>

        <!-- Second Row -->
        <div class="row mt-2">
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-tools text-dark"></i> علاوة فنية
                </label>
                <select name="technical_allowance" class="form-select form-select-sm" onchange="this.form.submit()">
                    <option value="">الكل</option>
                    <option value="yes" {% if technical_allowance == 'yes' %}selected{% endif %}>نعم</option>
                    <option value="no" {% if technical_allowance == 'no' %}selected{% endif %}>لا</option>
                </select>
            </div>

            <!-- Reset Button -->
            <div class="col-md-2 d-flex align-items-end">
                <a href="{% url 'ranks:employee_allowance_list' %}" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="fas fa-undo"></i> إعادة ضبط
                </a>
            </div>
        </div>

        <!-- Filter Results Info -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    عرض {{ total_allowances }} من علاوات الموظفين
                    {% if search_term or education_allowance or adjustment_allowance or transportation_allowance or supervisory_allowance or technical_allowance %}
                        (مفلترة)
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-3">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body text-center py-2">
                <div class="h4 mb-0">{{ total_allowances }}</div>
                <small>إجمالي السجلات</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body text-center py-2">
                <div class="h4 mb-0">{{ education_count }}</div>
                <small>علاوة التعليم</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body text-center py-2">
                <div class="h4 mb-0">{{ adjustment_count }}</div>
                <small>التجيير</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body text-center py-2">
                <div class="h4 mb-0">{{ transportation_count }}</div>
                <small>التنقلات</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center py-2">
                <div class="h4 mb-0">{{ supervisory_count }}</div>
                <small>العلاوة الإشرافية</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-dark text-white">
            <div class="card-body text-center py-2">
                <div class="h4 mb-0">{{ technical_count }}</div>
                <small>علاوة فنية</small>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع العلاوات</h6>
        <div class="d-flex align-items-center">
            <div class="input-group" style="width: 250px;">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" id="searchInput" class="form-control form-control-sm"
                       placeholder="بحث في العلاوات...">
            </div>
            <span class="badge bg-info ms-2">{{ total_allowances }} سجل</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="allowancesTable">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <i class="fas fa-hashtag text-primary"></i>
                            الرقم
                        </th>
                        <th width="12%">
                            <i class="fas fa-id-card text-primary"></i>
                            الرقم الوزاري
                        </th>
                        <th width="20%">
                            <i class="fas fa-user text-primary"></i>
                            الاسم الكامل
                        </th>
                        <th width="10%">
                            <i class="fas fa-graduation-cap text-success"></i>
                            علاوة التعليم
                        </th>
                        <th width="10%">
                            <i class="fas fa-cogs text-info"></i>
                            التجيير
                        </th>
                        <th width="10%">
                            <i class="fas fa-car text-warning"></i>
                            التنقلات
                        </th>
                        <th width="12%">
                            <i class="fas fa-user-tie text-secondary"></i>
                            العلاوة الإشرافية
                        </th>
                        <th width="10%">
                            <i class="fas fa-tools text-dark"></i>
                            علاوة فنية
                        </th>
                        <th width="11%">
                            <i class="fas fa-cogs text-dark"></i>
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for allowance in allowances %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ allowance.ministry_number|default:'-' }}</td>
                        <td>{{ allowance.employee.full_name }}</td>
                        <td class="text-center">
                            {% if allowance.education_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.adjustment_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.transportation_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.supervisory_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.technical_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'ranks:employee_allowance_update' allowance.pk %}"
                                   class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'ranks:employee_allowance_delete' allowance.pk %}"
                                   class="btn btn-danger btn-sm" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف علاوات هذا الموظف؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="alert alert-info mb-0">
                                لا توجد علاوات. <a href="{% url 'ranks:employee_allowance_create' %}">إضافة علاوات جديدة</a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
