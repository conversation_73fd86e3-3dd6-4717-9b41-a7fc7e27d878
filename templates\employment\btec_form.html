{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .form-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 20px;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }
    
    .employee-info {
        background: #f8f9ff;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
    }
    
    .employee-info.found {
        border-color: #28a745;
        background: #d4edda;
    }
    
    .employee-info.error {
        border-color: #dc3545;
        background: #f8d7da;
    }
    
    .loading-spinner {
        display: none;
        margin-left: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:btec_list' %}">معلمي BTEC</a></li>
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        {{ title }}
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" id="btecForm">
                        {% csrf_token %}
                        
                        <!-- Ministry Number -->
                        <div class="form-group">
                            <label for="{{ form.ministry_number.id_for_label }}" class="form-label">
                                <i class="fas fa-id-card me-2"></i>
                                {{ form.ministry_number.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.ministry_number }}
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i> جاري البحث...
                            </div>
                            {% if form.ministry_number.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.ministry_number.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Employee Info Display -->
                        <div id="employee-info" class="employee-info" style="display: none;">
                            <h6><i class="fas fa-user me-2"></i>معلومات الموظف</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">الاسم الكامل</label>
                                    {{ form.employee_name }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">القسم</label>
                                    {{ form.department_display }}
                                </div>
                                <div class="col-md-6 mt-3">
                                    <label class="form-label">التخصص</label>
                                    {{ form.specialization_display }}
                                </div>
                            </div>
                        </div>

                        <!-- Field Selection -->
                        <div class="form-group">
                            <label for="{{ form.field.id_for_label }}" class="form-label">
                                <i class="fas fa-tags me-2"></i>
                                {{ form.field.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.field }}
                            {% if form.field.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.field.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Hidden Fields -->
                        {{ form.employee }}

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-4">
                            <a href="{% url 'employment:btec_list' %}" class="btn btn-cancel text-white">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-save text-white">
                                <i class="fas fa-save me-2"></i>حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize Select2 for field dropdown
    $('#id_field').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الحقل...',
        allowClear: false
    });

    // Handle ministry number input
    $('#ministry_number_input').on('input', function() {
        const ministryNumber = $(this).val().trim();
        const $employeeInfo = $('#employee-info');
        const $loadingSpinner = $('.loading-spinner');
        
        if (ministryNumber.length >= 3) {
            $loadingSpinner.show();
            
            $.ajax({
                url: '{% url "employment:get_employee_by_ministry_number" %}',
                data: { 'ministry_number': ministryNumber },
                success: function(response) {
                    $loadingSpinner.hide();
                    
                    if (response.success) {
                        // Fill employee data
                        $('#employee_name_display').val(response.employee.full_name);
                        $('#department_display').val(response.employee.department);
                        $('#specialization_display').val(response.employee.specialization);
                        $('#id_employee').val(response.employee.id);
                        
                        // Show employee info with success styling
                        $employeeInfo.removeClass('error').addClass('found').show();
                    } else {
                        // Show error
                        $employeeInfo.removeClass('found').addClass('error').show();
                        $('#employee_name_display').val('');
                        $('#department_display').val('');
                        $('#specialization_display').val('');
                        $('#id_employee').val('');
                    }
                },
                error: function() {
                    $loadingSpinner.hide();
                    $employeeInfo.removeClass('found').addClass('error').show();
                    $('#employee_name_display').val('');
                    $('#department_display').val('');
                    $('#specialization_display').val('');
                    $('#id_employee').val('');
                }
            });
        } else {
            $employeeInfo.hide();
            $('#employee_name_display').val('');
            $('#department_display').val('');
            $('#specialization_display').val('');
            $('#id_employee').val('');
        }
    });

    // Trigger search if ministry number is pre-filled (for edit mode)
    if ($('#ministry_number_input').val()) {
        $('#ministry_number_input').trigger('input');
    }
});
</script>
{% endblock %}
