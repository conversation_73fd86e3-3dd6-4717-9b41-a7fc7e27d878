{% extends 'base.html' %}
{% load static %}

{% block title %}معلمي BTEC{% endblock %}

{% block extra_css %}
<style>
    .filter-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        border: 3px solid #000;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        position: relative;
    }

    .filter-card::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
        border-radius: 18px;
        z-index: -1;
        opacity: 0.5;
    }

    .filter-card .card-header {
        background: rgba(0, 0, 0, 0.3);
        border: none;
        border-radius: 12px 12px 0 0;
        border-bottom: 2px solid rgba(0, 0, 0, 0.4);
        color: white !important;
    }

    .filter-card .card-header h6 {
        color: white !important;
    }

    .filter-card .card-header i {
        color: white !important;
    }

    /* تأكيد أن جميع النصوص في header الفلتر بيضاء */
    .filter-card .card-header * {
        color: white !important;
    }

    .filter-card .card-header .font-weight-bold,
    .filter-card .card-header .m-0 {
        color: white !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        font-weight: 700 !important;
    }

    /* تحسين مظهر الأيقونة */
    .filter-card .card-header .fas {
        color: white !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        margin-right: 8px;
    }

    .filter-card .form-control,
    .filter-card .form-select {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(0, 0, 0, 0.3);
        border-radius: 8px;
        color: #000;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .filter-card .form-control:focus,
    .filter-card .form-select:focus {
        background: white;
        border-color: #000;
        box-shadow: 0 0 0 0.3rem rgba(0, 0, 0, 0.2);
        transform: translateY(-1px);
    }

    .filter-card .form-label {
        color: #000;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 700;
        text-shadow: none;
        margin-bottom: 8px;
        display: inline-block;
        border: 1px solid rgba(0, 0, 0, 0.2);
    }

    .filter-card .btn-light {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(0, 0, 0, 0.3);
        color: #000;
        font-weight: 700;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .filter-card .btn-light:hover {
        background: white;
        border-color: #000;
        color: #000;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .filter-card .btn-outline-light {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.9);
        color: white;
        font-weight: 700;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .filter-card .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: white;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        padding: 15px 10px;
    }

    .table tbody td {
        text-align: center;
        vertical-align: middle;
        padding: 12px 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody tr:hover {
        background-color: #f8f9ff;
        transform: scale(1.01);
        transition: all 0.3s ease;
    }

    .btn-action {
        padding: 6px 12px;
        margin: 2px;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        text-align: center;
        padding: 20px;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    /* Modal Styles */
    .info-item {
        margin-bottom: 1.5rem;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #17a2b8;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        display: block;
        margin-bottom: 8px;
        font-size: 0.95rem;
    }

    .info-value {
        font-weight: 500;
        color: #212529;
        font-size: 1rem;
    }

    .info-description {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        margin-top: 8px;
        font-style: italic;
        color: #6c757d;
    }

    .modal-header.bg-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    }

    .btn-action {
        margin: 1px;
    }

    /* Custom styling for ministry number badge */
    .ministry-number-badge {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        color: white !important;
        font-weight: 700 !important;
        padding: 8px 12px !important;
        font-size: 0.95rem !important;
        border: 2px solid #0056b3 !important;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                معلمي BTEC
            </h1>
            <p class="text-muted mb-0">إدارة معلمي BTEC والحقول التعليمية</p>
        </div>
        <div>
            <a href="{% url 'employment:btec_create' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus"></i> إضافة معلم BTEC
            </a>
            <a href="{% url 'employment:btec_field_list' %}" class="btn btn-info me-2">
                <i class="fas fa-list"></i> إدارة الحقول
            </a>
            <a href="{% url 'employment:btec_job_list' %}" class="btn btn-success me-2">
                <i class="fas fa-briefcase"></i> إدارة الوظائف
            </a>
            <a href="?export=excel{% if search_term %}&search={{ search_term }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if field_filter %}&field={{ field_filter }}{% endif %}{% if specialization_filter %}&specialization={{ specialization_filter }}{% endif %}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ total_teachers }}</div>
                <div class="stats-label">إجمالي معلمي BTEC</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ total_departments }}</div>
                <div class="stats-label">إجمالي الأقسام</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ total_fields }}</div>
                <div class="stats-label">إجمالي الحقول</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ total_jobs }}</div>
                <div class="stats-label">إجمالي الوظائف</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ total_specializations }}</div>
                <div class="stats-label">إجمالي التخصصات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number" id="filteredCount">{{ total_teachers }}</div>
                <div class="stats-label">النتائج المعروضة</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card filter-card mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-filter me-2"></i>
                البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row g-3 align-items-end">
                    <div class="col-md-2">
                        <label for="search" class="form-label fw-bold">البحث</label>
                        <div class="d-flex gap-2">
                            <input type="text" name="search" id="search" class="form-control"
                                   value="{{ search_term }}" placeholder="الرقم الوزاري أو الاسم">
                            <button type="submit" class="btn btn-light btn-sm">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="department" class="form-label fw-bold">القسم</label>
                        {{ search_form.department }}
                    </div>
                    <div class="col-md-2">
                        <label for="field" class="form-label fw-bold">الحقل</label>
                        {{ search_form.field }}
                    </div>
                    <div class="col-md-2">
                        <label for="job" class="form-label fw-bold">الوظيفة</label>
                        {{ search_form.job }}
                    </div>
                    <div class="col-md-2">
                        <label for="specialization" class="form-label fw-bold">التخصص</label>
                        {{ search_form.specialization }}
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'employment:btec_list' %}" class="btn btn-outline-light w-100">
                            <i class="fas fa-undo"></i> إعادة ضبط
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Table -->
    <div class="table-container">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th style="width: 8%;">
                            <i class="fas fa-hashtag me-2"></i>الرقم
                        </th>
                        <th style="width: 15%;">
                            <i class="fas fa-id-card me-2"></i>الرقم الوزاري
                        </th>
                        <th style="width: 25%;">
                            <i class="fas fa-user me-2"></i>الاسم الكامل
                        </th>
                        <th style="width: 18%;">
                            <i class="fas fa-graduation-cap me-2"></i>التخصص
                        </th>
                        <th style="width: 15%;">
                            <i class="fas fa-briefcase me-2"></i>الوظيفة
                        </th>
                        <th style="width: 18%;">
                            <i class="fas fa-school me-2"></i>المدرسة
                        </th>
                        <th style="width: 14%;">
                            <i class="fas fa-tags me-2"></i>الحقل
                        </th>
                        <th style="width: 12%;">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for btec_teacher in btec_teachers %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>
                            <span class="badge bg-primary">{{ btec_teacher.ministry_number }}</span>
                        </td>
                        <td>
                            <strong>{{ btec_teacher.employee.full_name }}</strong>
                        </td>
                        <td>{{ btec_teacher.specialization }}</td>
                        <td>
                            <span class="badge bg-success">{{ btec_teacher.job.name }}</span>
                        </td>
                        <td>{{ btec_teacher.department_name }}</td>
                        <td>
                            <span class="badge bg-info">{{ btec_teacher.field.name }}</span>
                        </td>
                        <td>
                            <a href="#" class="btn btn-info btn-action" title="معاينة"
                               data-bs-toggle="modal" data-bs-target="#viewModal{{ btec_teacher.pk }}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'employment:btec_update' btec_teacher.pk %}"
                               class="btn btn-warning btn-action" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'employment:btec_delete' btec_teacher.pk %}"
                               class="btn btn-danger btn-action" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>

                    <!-- View Modal for each teacher -->
                    <div class="modal fade" id="viewModal{{ btec_teacher.pk }}" tabindex="-1" aria-labelledby="viewModalLabel{{ btec_teacher.pk }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header bg-info text-white">
                                    <h5 class="modal-title" id="viewModalLabel{{ btec_teacher.pk }}">
                                        <i class="fas fa-eye me-2"></i>
                                        معاينة معلم BTEC
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-id-card me-2"></i>الرقم الوزاري:
                                                </label>
                                                <span class="info-value badge bg-primary ministry-number-badge">{{ btec_teacher.ministry_number }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-user me-2"></i>الاسم الكامل:
                                                </label>
                                                <span class="info-value">{{ btec_teacher.employee.full_name }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-graduation-cap me-2"></i>التخصص:
                                                </label>
                                                <span class="info-value">{{ btec_teacher.specialization }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-school me-2"></i>المدرسة:
                                                </label>
                                                <span class="info-value">{{ btec_teacher.department_name }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-tags me-2"></i>حقل BTEC:
                                                </label>
                                                <span class="info-value badge bg-info">{{ btec_teacher.field.name }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-briefcase me-2"></i>الوظيفة:
                                                </label>
                                                <span class="info-value badge bg-success">{{ btec_teacher.job.name }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-calendar me-2"></i>تاريخ الإضافة:
                                                </label>
                                                <span class="info-value">{{ btec_teacher.created_at|date:"Y/m/d H:i" }}</span>
                                            </div>
                                        </div>
                                        {% if btec_teacher.field.description %}
                                        <div class="col-12">
                                            <div class="info-item">
                                                <label class="info-label">
                                                    <i class="fas fa-info-circle me-2"></i>وصف الحقل:
                                                </label>
                                                <div class="info-description">{{ btec_teacher.field.description }}</div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-2"></i>إغلاق
                                    </button>
                                    <a href="{% url 'employment:btec_update' btec_teacher.pk %}" class="btn btn-warning">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات معلمي BTEC</p>
                            <a href="{% url 'employment:btec_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة معلم BTEC الأول
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر...',
        allowClear: true,
        width: '100%'
    });

    // Custom styling for Select2 in filter card
    $('.filter-card .select2-container .select2-selection--single').css({
        'background': 'rgba(255, 255, 255, 0.95)',
        'border': '2px solid rgba(0, 0, 0, 0.3)',
        'border-radius': '8px',
        'height': '38px',
        'line-height': '34px',
        'color': '#000',
        'font-weight': '600'
    });

    $('.filter-card .select2-container .select2-selection--single:focus').css({
        'background': 'white',
        'border-color': '#000',
        'box-shadow': '0 0 0 0.3rem rgba(0, 0, 0, 0.2)'
    });

    // Style Select2 rendered text
    $('.filter-card .select2-container .select2-selection__rendered').css({
        'color': '#000',
        'font-weight': '600'
    });

    // Handle form submission with Enter key
    $('#search').on('keypress', function(e) {
        if (e.which === 13) {
            $(this).closest('form').submit();
        }
    });

    // Auto-submit form when filters change
    $('.select2').on('change', function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}
