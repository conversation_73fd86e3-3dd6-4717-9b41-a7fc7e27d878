from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count
from django.http import HttpResponse, JsonResponse
from django.conf import settings
from django.urls import reverse
from urllib.parse import urlencode
import io
from datetime import date, timedelta
import os
# Avoid importing docx directly
try:
    import docx
except ImportError:
    docx = None
# Avoid importing pandas directly
try:
    import pandas as pd
except ImportError:
    pd = None
from .models import Department, Position, EmploymentStatus, Employment, TechnicalPosition, EmployeePosition, EmployeeIdentification, AppointmentType, ExcessEmployee, MedicalCondition, MedicalConditionName, BtecField, BtecJob, BtecTeacher, EmployeeAllowance, NonPayment
from .forms import (
    DepartmentForm, PositionForm, EmploymentStatusForm, EmploymentForm, DepartmentImportForm,
    TechnicalPositionForm, EmployeePositionForm, EmployeeIdentificationForm, IdNumberImportForm,
    AppointmentTypeForm, ExcessEmployeeForm, ExcessEmployeeSearchForm, ExcessEmployeeResolveForm,
    MedicalConditionForm, MedicalConditionSearchForm, MedicalConditionNameForm, BtecFieldForm, BtecJobForm, BtecTeacherForm, BtecSearchForm,
    EmployeeAllowanceForm, EmployeeAllowanceSearchForm, NonPaymentForm, NonPaymentSearchForm
)
from employees.models import Employee
from leaves.models import Leave, LeaveType
from notifications.utils import create_notification

@login_required
def employment_list(request):
    if request.method == 'POST':
        # Handle form submission for adding new employment
        employee_id = request.POST.get('employee')
        department_id = request.POST.get('department')
        position_id = request.POST.get('position')
        status_id = request.POST.get('status')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        is_current = request.POST.get('is_current') == 'true'

        # Create new employment record
        employment = Employment(
            employee_id=employee_id,
            department_id=department_id,
            position_id=position_id,
            status_id=status_id,
            start_date=start_date,
            end_date=end_date if end_date else None,
            is_current=is_current
        )
        employment.save()
        messages.success(request, 'تم إضافة الموظف بنجاح.')
        return redirect('employment:employment_list')

    # Get data for the list view
    search_query = request.GET.get('search', '')
    if search_query:
        employments = Employment.objects.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(department__name__icontains=search_query) |
            Q(position__name__icontains=search_query)
        )
    else:
        employments = Employment.objects.all()

    # Get data for the add employment form
    employees = Employee.objects.all()
    departments = Department.objects.all()
    positions = Position.objects.all()
    statuses = EmploymentStatus.objects.all()

    return render(request, 'employment/employment_list.html', {
        'employments': employments,
        'employees': employees,
        'departments': departments,
        'positions': positions,
        'statuses': statuses,
        'search_query': search_query
    })

@login_required
def department_list(request):
    departments = Department.objects.all()

    # Handle AJAX requests for inline editing
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        department_id = request.POST.get('department_id')
        field_name = request.POST.get('field_name')
        field_value = request.POST.get('field_value')

        try:
            department = Department.objects.get(id=department_id)

            # Validate field name
            allowed_fields = ['name', 'description', 'workplace', 'school_type', 'school_gender', 'highest_grade', 'lowest_grade', 'directorate_type']
            if field_name not in allowed_fields:
                return JsonResponse({'success': False, 'error': 'حقل غير مسموح'})

            # Set the field value
            setattr(department, field_name, field_value if field_value else None)
            department.save()

            # Get display value for choice fields
            display_value = field_value
            if field_name == 'workplace':
                display_value = department.get_workplace_display()
            elif field_name == 'school_type':
                display_value = department.get_school_type_display() if department.school_type else '-'
            elif field_name == 'school_gender':
                display_value = department.get_school_gender_display() if department.school_gender else '-'
            elif field_name == 'highest_grade':
                if department.highest_grade:
                    display_value = f'<span class="badge bg-success"><i class="fas fa-arrow-up me-1"></i>{department.get_highest_grade_display()}</span>'
                else:
                    display_value = '<span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span>'
            elif field_name == 'lowest_grade':
                if department.lowest_grade:
                    display_value = f'<span class="badge bg-danger"><i class="fas fa-arrow-down me-1"></i>{department.get_lowest_grade_display()}</span>'
                else:
                    display_value = '<span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span>'
            elif field_name == 'directorate_type':
                display_value = department.get_directorate_type_display() if department.directorate_type else '-'

            return JsonResponse({
                'success': True,
                'display_value': display_value,
                'message': 'تم حفظ التغييرات بنجاح'
            })

        except Department.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'القسم غير موجود'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return render(request, 'employment/department_list.html', {'departments': departments})

@login_required
def department_create(request):
    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            department = form.save()

            # Create notification for new department
            create_notification(
                title='قسم جديد تم إضافته',
                message=f'تم إضافة قسم جديد: {department.name}',
                notification_type='success',
                icon='fa-building',
                user=request.user,
                is_global=False
            )

            # Log the action with detailed description
            from system_logs.views import log_user_action
            from system_logs.models import SystemLog
            log_user_action(
                request=request,
                user=request.user,
                module=SystemLog.EMPLOYMENT,
                action=SystemLog.CREATE,
                page='إضافة قسم جديد',
                description=f'قام المستخدم <strong>{request.user.username}</strong> بإضافة قسم جديد <span class="object-name">({department.name})</span>',
                object_id=str(department.pk),
                object_repr=department.name
            )

            messages.success(request, 'تم إضافة القسم بنجاح.')
            return redirect('employment:department_detail', pk=department.pk)
    else:
        form = DepartmentForm()
    return render(request, 'employment/department_form.html', {'form': form})

@login_required
def department_detail(request, pk):
    department = get_object_or_404(Department, pk=pk)
    employees = Employment.objects.filter(department=department, is_current=True)
    return render(request, 'employment/department_detail.html', {
        'department': department,
        'employees': employees
    })

@login_required
def department_update(request, pk):
    department = get_object_or_404(Department, pk=pk)
    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            try:
                form.save()

                # Create notification for department update
                create_notification(
                    title='تحديث بيانات قسم',
                    message=f'تم تحديث بيانات القسم: {department.name}',
                    notification_type='info',
                    icon='fa-edit',
                    user=request.user,
                    is_global=False
                )

                # Log the action with detailed description
                from system_logs.views import log_user_action
                from system_logs.models import SystemLog
                log_user_action(
                    request=request,
                    user=request.user,
                    module=SystemLog.EMPLOYMENT,
                    action=SystemLog.UPDATE,
                    page='تحديث بيانات قسم',
                    description=f'قام المستخدم <strong>{request.user.username}</strong> بتحديث بيانات القسم <span class="object-name">({department.name})</span>',
                    object_id=str(department.pk),
                    object_repr=department.name
                )

                messages.success(request, 'تم تحديث بيانات القسم بنجاح.')
                return redirect('employment:department_detail', pk=department.pk)
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ البيانات: {str(e)}')
        else:
            # Add form errors to messages
            for field, errors in form.errors.items():
                for error in errors:
                    if field == '__all__':
                        messages.error(request, f'خطأ: {error}')
                    else:
                        field_name = form.fields[field].label or field
                        messages.error(request, f'خطأ في {field_name}: {error}')
    else:
        form = DepartmentForm(instance=department)
    return render(request, 'employment/department_form.html', {'form': form, 'department': department})

@login_required
def department_delete(request, pk):
    department = get_object_or_404(Department, pk=pk)

    # Check if there are employees associated with this department
    employees_count = Employment.objects.filter(department=department, is_current=True).count()

    if employees_count > 0:
        messages.error(request, f'لا يمكن حذف القسم لأنه مرتبط بـ {employees_count} موظف. قم بنقل الموظفين إلى قسم آخر أولاً.')
        return redirect('employment:department_detail', pk=department.pk)

    if request.method == 'POST':
        department.delete()
        messages.success(request, 'تم حذف القسم بنجاح.')
        return redirect('employment:department_list')

    return render(request, 'employment/department_confirm_delete.html', {'department': department})

@login_required
def department_import(request):
    if request.method == 'POST':
        form = DepartmentImportForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['file']

            # Check if pandas is available
            if pd is None:
                messages.error(request, 'مكتبة pandas غير متوفرة. يرجى تثبيتها أولاً.')
                return render(request, 'employment/department_import.html', {'form': form})

            try:
                # Read the Excel file
                df = pd.read_excel(excel_file)

                # Count successful imports
                success_count = 0
                error_count = 0

                # Process each row
                for index, row in df.iterrows():
                    try:
                        name = row['الاسم']
                        description = row.get('الوصف', '')
                        workplace = row.get('مكان العمل', 'directorate')

                        # Convert workplace to English values
                        workplace_mapping = {
                            'المدارس': 'school',
                            'المديرية': 'directorate',
                            'school': 'school',
                            'directorate': 'directorate'
                        }
                        workplace = workplace_mapping.get(workplace, 'directorate')

                        # Check if department already exists
                        if not Department.objects.filter(name=name).exists():
                            # Create department with basic info
                            department = Department.objects.create(
                                name=name,
                                description=description,
                                workplace=workplace
                            )

                            # Add school-specific fields if workplace is school
                            if workplace == 'school':
                                # School type mapping
                                school_type_mapping = {
                                    'أساسي': 'primary',
                                    'ثانوي': 'secondary',
                                    'أساسي + ثانوي': 'primary_secondary',
                                    'primary': 'primary',
                                    'secondary': 'secondary',
                                    'primary_secondary': 'primary_secondary'
                                }

                                # School gender mapping
                                school_gender_mapping = {
                                    'ذكور': 'male',
                                    'إناث': 'female',
                                    'مختلط': 'mixed',
                                    'male': 'male',
                                    'female': 'female',
                                    'mixed': 'mixed'
                                }

                                # Grade mapping
                                grade_mapping = {
                                    'رياض الأطفال': 'kg',
                                    'kg': 'kg',
                                    'grade1': 'grade1', 'grade2': 'grade2', 'grade3': 'grade3',
                                    'grade4': 'grade4', 'grade5': 'grade5', 'grade6': 'grade6',
                                    'grade7': 'grade7', 'grade8': 'grade8', 'grade9': 'grade9',
                                    'grade10': 'grade10', 'grade11': 'grade11', 'grade12': 'grade12'
                                }

                                # Set school fields if provided
                                school_type = row.get('تصنيف المدرسة', '')
                                if school_type:
                                    department.school_type = school_type_mapping.get(school_type)

                                school_gender = row.get('جنس المدرسة', '')
                                if school_gender:
                                    department.school_gender = school_gender_mapping.get(school_gender)

                                highest_grade = row.get('أعلى صف', '')
                                if highest_grade:
                                    department.highest_grade = grade_mapping.get(highest_grade)

                                lowest_grade = row.get('أدنى صف', '')
                                if lowest_grade:
                                    department.lowest_grade = grade_mapping.get(lowest_grade)

                            elif workplace == 'directorate':
                                # Directorate type mapping
                                directorate_type_mapping = {
                                    'يتبع للمدير': 'manager',
                                    'الأقسام الإدارية': 'administrative',
                                    'الأقسام التعليمية': 'educational',
                                    'manager': 'manager',
                                    'administrative': 'administrative',
                                    'educational': 'educational'
                                }

                                directorate_type = row.get('يتبع لـ', '')
                                if directorate_type:
                                    department.directorate_type = directorate_type_mapping.get(directorate_type)

                            # Save the department with all fields
                            department.save()
                            success_count += 1
                        else:
                            error_count += 1
                    except Exception as e:
                        error_count += 1
                        continue

                if success_count > 0:
                    messages.success(request, f'تم استيراد {success_count} قسم بنجاح.')
                if error_count > 0:
                    messages.warning(request, f'فشل استيراد {error_count} قسم (إما بسبب وجوده مسبقاً أو بيانات غير صالحة).')

                return redirect('employment:department_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء معالجة الملف: {str(e)}')
    else:
        form = DepartmentImportForm()

    return render(request, 'employment/department_import.html', {'form': form})

@login_required
def department_employees(request, pk):
    department = get_object_or_404(Department, pk=pk)
    employees = Employment.objects.filter(department=department)
    return render(request, 'employment/department_employees.html', {
        'department': department,
        'employees': employees
    })

# المسميات الوظيفية
@login_required
def position_list(request):
    positions = Position.objects.annotate(employee_count=Count('employees', filter=Q(employees__is_current=True)))
    return render(request, 'employment/position_list.html', {'positions': positions})


# الموقف الفني
@login_required
def technical_position_list(request):
    """View for listing technical positions"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Filter technical positions
    if search_query:
        technical_positions = TechnicalPosition.objects.filter(
            Q(specialization__icontains=search_query)
        ).order_by('specialization')
    else:
        technical_positions = TechnicalPosition.objects.all().order_by('specialization')

    # Calculate total vacancies by specialization
    specialization_totals = {}
    for position in technical_positions:
        if position.specialization in specialization_totals:
            specialization_totals[position.specialization] += position.vacancies
        else:
            specialization_totals[position.specialization] = position.vacancies

    # Group positions by specialization
    grouped_positions = {}
    for position in technical_positions:
        if position.specialization not in grouped_positions:
            grouped_positions[position.specialization] = {
                'positions': [],
                'total_vacancies': specialization_totals[position.specialization],
                'first_row': True  # Flag to mark the first row for each specialization
            }
        grouped_positions[position.specialization]['positions'].append(position)

    # Calculate grand total of all vacancies
    total_vacancies = sum(specialization_totals.values())

    return render(request, 'employment/technical_position_list.html', {
        'technical_positions': technical_positions,
        'search_query': search_query,
        'specialization_totals': specialization_totals,
        'grouped_positions': grouped_positions,
        'total_vacancies': total_vacancies
    })

@login_required
def technical_position_create(request):
    """View for creating a new technical position"""
    # Get all school departments
    school_departments = Department.objects.filter(workplace='school').order_by('name')

    if request.method == 'POST':
        # Get basic form data
        specialization = request.POST.get('specialization')
        gender = request.POST.get('gender')
        vacancies_count = int(request.POST.get('vacancies', 0))

        # Validate basic data
        if not specialization or not gender or vacancies_count <= 0:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return redirect('employment:technical_position_create')

        # Process each vacancy
        created_count = 0
        for i in range(vacancies_count):
            department_id = request.POST.get(f'department_{i}')
            reason = request.POST.get(f'reason_{i}')

            if department_id and reason:
                try:
                    department = Department.objects.get(id=department_id)

                    # Create technical position
                    TechnicalPosition.objects.create(
                        specialization=specialization,
                        gender=gender,
                        vacancies=1,  # Each position has 1 vacancy
                        department=department,
                        notes=reason
                    )
                    created_count += 1
                except Department.DoesNotExist:
                    messages.error(request, f'القسم المحدد للشاغر رقم {i+1} غير موجود.')

        if created_count > 0:
            messages.success(request, f'تم إضافة {created_count} موقف فني بنجاح.')
            return redirect('employment:technical_position_list')
        else:
            messages.error(request, 'لم يتم إضافة أي موقف فني. يرجى التحقق من البيانات المدخلة.')

    # Create empty form for initial display
    form = TechnicalPositionForm()

    return render(request, 'employment/technical_position_create.html', {
        'form': form,
        'school_departments': school_departments
    })


@login_required
def technical_position_update(request, pk):
    """View for updating a technical position"""
    technical_position = get_object_or_404(TechnicalPosition, pk=pk)

    # Get all school departments
    school_departments = Department.objects.filter(workplace='school').order_by('name')

    if request.method == 'POST':
        form = TechnicalPositionForm(request.POST, instance=technical_position)
        if form.is_valid():
            # Get the selected school departments
            selected_departments = form.cleaned_data.get('school_departments', [])
            vacancies_count = form.cleaned_data.get('vacancies', 0)

            # Check if we have selected departments
            if selected_departments and len(selected_departments) > 0:
                # Update the current technical position with the first department
                if selected_departments:
                    technical_position = form.save(commit=False)
                    technical_position.department = selected_departments[0]
                    technical_position.vacancies = 1  # Each position has 1 vacancy
                    technical_position.save()

                # Create additional technical positions for the remaining departments
                for i, department in enumerate(selected_departments[1:], 1):
                    if i >= vacancies_count:
                        break

                    # Create a new technical position with this department
                    new_position = TechnicalPosition(
                        specialization=technical_position.specialization,
                        department=department,
                        gender=technical_position.gender,
                        vacancies=1,  # Each position has 1 vacancy
                        notes=technical_position.notes
                    )
                    new_position.save()

                messages.success(request, f'تم تحديث الموقف الفني وإضافة {len(selected_departments)-1} موقف فني جديد بنجاح.')
            else:
                messages.error(request, 'يجب اختيار قسم مدرسي واحد على الأقل.')
                return render(request, 'employment/technical_position_form.html', {
                    'form': form,
                    'technical_position': technical_position,
                    'school_departments': school_departments
                })

            return redirect('employment:technical_position_list')
    else:
        form = TechnicalPositionForm(instance=technical_position)

        # Pre-select the current department if it's a school department
        if technical_position.department and technical_position.department.workplace == 'school':
            form.fields['school_departments'].initial = [technical_position.department.id]

    return render(request, 'employment/technical_position_form.html', {
        'form': form,
        'technical_position': technical_position,
        'school_departments': school_departments
    })


@login_required
def technical_position_delete(request, pk):
    """View for deleting a technical position"""
    technical_position = get_object_or_404(TechnicalPosition, pk=pk)

    if request.method == 'POST':
        technical_position.delete()
        messages.success(request, 'تم حذف الموقف الفني بنجاح.')
        return redirect('employment:technical_position_list')

    return render(request, 'employment/technical_position_confirm_delete.html', {
        'technical_position': technical_position
    })

@login_required
def export_technical_positions_excel(request):
    """View for exporting technical positions to Excel"""
    # Get search parameter if any
    search_query = request.GET.get('search', '')

    # Check if pandas is available
    if pd is None:
        messages.error(request, 'مكتبة pandas غير متوفرة. يرجى تثبيتها أولاً.')
        return redirect('employment:technical_position_list')

    # Filter technical positions
    if search_query:
        technical_positions = TechnicalPosition.objects.filter(
            Q(specialization__icontains=search_query)
        ).order_by('specialization')
    else:
        technical_positions = TechnicalPosition.objects.all().order_by('specialization')

    # Calculate total vacancies by specialization
    specialization_totals = {}
    for position in technical_positions:
        if position.specialization in specialization_totals:
            specialization_totals[position.specialization] += position.vacancies
        else:
            specialization_totals[position.specialization] = position.vacancies

    # Group positions by specialization
    grouped_positions = {}
    for position in technical_positions:
        if position.specialization not in grouped_positions:
            grouped_positions[position.specialization] = {
                'positions': [],
                'total_vacancies': specialization_totals[position.specialization]
            }
        grouped_positions[position.specialization]['positions'].append(position)

    # Create a response with Excel content type
    response = HttpResponse(content_type='application/vnd.ms-excel')

    # Add search query to filename if exists
    filename = "technical_positions"
    if search_query:
        # Clean search query for filename
        clean_query = ''.join(c for c in search_query if c.isalnum() or c.isspace())
        filename += f"_search_{clean_query}"

    response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'

    # Create Excel workbook
    output = io.BytesIO()
    workbook = pd.ExcelWriter(output, engine='xlsxwriter')

    # Create workbook and worksheet
    worksheet = workbook.book.add_worksheet('المواقف الفنية')
    worksheet.right_to_left()  # Set RTL for worksheet

    # Define formats
    header_format = workbook.book.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'top',
        'fg_color': '#D7E4BC',
        'border': 1,
        'align': 'center'
    })

    specialization_format = workbook.book.add_format({
        'bold': True,
        'valign': 'middle',
        'align': 'center',
        'bg_color': 'rgba(78, 115, 223, 0.05)',
        'border': 1
    })

    total_vacancies_format = workbook.book.add_format({
        'bold': True,
        'valign': 'middle',
        'align': 'center',
        'bg_color': '#e8f4ea',
        'border': 1,
        'font_size': 11
    })

    cell_format = workbook.book.add_format({
        'valign': 'middle',
        'align': 'center',
        'border': 1
    })

    # Write headers
    headers = ['التخصص', 'القسم', 'الجنس', 'عدد الشواغر', 'المبرر', 'الإجراءات']
    for col_num, header in enumerate(headers):
        worksheet.write(0, col_num, header, header_format)

    # Write data with merged cells for specialization and total vacancies
    row = 1
    for specialization, data in grouped_positions.items():
        positions = data['positions']
        total_vacancies = data['total_vacancies']

        # Merge cells for specialization and total vacancies if more than one position
        if len(positions) > 1:
            worksheet.merge_range(row, 0, row + len(positions) - 1, 0, specialization, specialization_format)
            worksheet.merge_range(row, 3, row + len(positions) - 1, 3, total_vacancies, total_vacancies_format)
        else:
            worksheet.write(row, 0, specialization, specialization_format)
            worksheet.write(row, 3, total_vacancies, total_vacancies_format)

        # Write each position's details
        for i, position in enumerate(positions):
            current_row = row + i

            # Write department
            worksheet.write(current_row, 1, position.department.name if position.department else '-', cell_format)

            # Write gender
            worksheet.write(current_row, 2, position.get_gender_display(), cell_format)

            # Write notes
            worksheet.write(current_row, 4, position.notes, cell_format)

            # Leave actions column empty
            worksheet.write(current_row, 5, '', cell_format)

        # Move to next row after this specialization group
        row += len(positions)

    # Adjust column widths
    column_widths = [25, 25, 15, 15, 40, 15]
    for i, width in enumerate(column_widths):
        worksheet.set_column(i, i, width)

    # Create summary sheet
    summary_worksheet = workbook.book.add_worksheet('ملخص')
    summary_worksheet.right_to_left()

    # Format for summary sheet
    summary_header_format = workbook.book.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'center',
        'align': 'center',
        'fg_color': '#4F81BD',
        'font_color': 'white',
        'border': 1,
        'font_size': 12
    })

    summary_data_format = workbook.book.add_format({
        'valign': 'center',
        'align': 'center',
        'border': 1,
        'font_size': 11
    })

    # Write summary headers
    summary_headers = ['البيان', 'القيمة']
    for col_num, header in enumerate(summary_headers):
        summary_worksheet.write(0, col_num, header, summary_header_format)

    # Write summary data
    summary_data = [
        ['إجمالي عدد المواقف الفنية', len(technical_positions)],
        ['إجمالي عدد الشواغر', sum(position.vacancies for position in technical_positions)],
        ['تاريخ التصدير', date.today().strftime('%Y-%m-%d')]
    ]

    for row_num, row_data in enumerate(summary_data):
        for col_num, cell_data in enumerate(row_data):
            summary_worksheet.write(row_num + 1, col_num, cell_data, summary_data_format)

    # Adjust column widths for summary sheet
    summary_worksheet.set_column(0, 0, 25)  # البيان
    summary_worksheet.set_column(1, 1, 15)  # القيمة

    # Save the workbook
    workbook.close()

    # Get the output and write it to the response
    output.seek(0)
    response.write(output.getvalue())

    return response


@login_required
def actual_service_list(request):
    """View for displaying employees' actual service"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Filter employees
    if search_query:
        employees = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(full_name__icontains=search_query)
        ).order_by('full_name')
    else:
        employees = Employee.objects.all().order_by('full_name')

    # Calculate actual service for each employee
    employees_data = []
    for employee in employees:
        # Get current employment details
        current_employment = employee.employments.filter(is_current=True).first()
        position_name = current_employment.position.name if current_employment and current_employment.position else ''
        department_name = current_employment.department.name if current_employment and current_employment.department else employee.school

        # Calculate service duration
        today = date.today()
        hire_date = employee.hire_date
        if hire_date:
            # Calculate total days between hire date and today
            total_days = (today - hire_date).days

            # Get unpaid leaves
            unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
            unpaid_leave_days = 0

            if unpaid_leave_type:
                unpaid_leaves = Leave.objects.filter(
                    employee=employee,
                    leave_type=unpaid_leave_type,
                    status='approved'
                )

                # Sum up all unpaid leave days
                for leave in unpaid_leaves:
                    unpaid_leave_days += leave.days_count

            # Calculate actual service days
            actual_service_days = total_days - unpaid_leave_days

            # Convert to years, months, days
            years = actual_service_days // 365
            remaining_days = actual_service_days % 365
            months = remaining_days // 30
            days = remaining_days % 30

            actual_service = f"{years} سنة, {months} شهر, {days} يوم"
        else:
            actual_service = 'غير متوفر'

        # Add to employees data list
        employees_data.append({
            'id': employee.id,
            'ministry_number': employee.ministry_number,
            'full_name': employee.full_name,
            'position': position_name,
            'department': department_name,
            'hire_date': hire_date,
            'actual_service': actual_service
        })

    return render(request, 'employment/actual_service_list.html', {
        'employees_data': employees_data,
        'search_query': search_query
    })

@login_required
def position_create(request):
    if request.method == 'POST':
        form = PositionForm(request.POST)
        if form.is_valid():
            position = form.save()
            messages.success(request, 'تم إضافة المسمى الوظيفي بنجاح.')
            return redirect('employment:position_detail', pk=position.pk)
    else:
        form = PositionForm()
    return render(request, 'employment/position_form.html', {'form': form})

@login_required
def position_detail(request, pk):
    position = get_object_or_404(Position, pk=pk)
    employees = Employment.objects.filter(position=position, is_current=True)
    return render(request, 'employment/position_detail.html', {
        'position': position,
        'employees': employees
    })

@login_required
def position_update(request, pk):
    position = get_object_or_404(Position, pk=pk)
    if request.method == 'POST':
        form = PositionForm(request.POST, instance=position)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث المسمى الوظيفي بنجاح.')
            return redirect('employment:position_detail', pk=position.pk)
    else:
        form = PositionForm(instance=position)
    return render(request, 'employment/position_form.html', {'form': form, 'position': position})

@login_required
def position_delete(request, pk):
    position = get_object_or_404(Position, pk=pk)
    if request.method == 'POST':
        position.delete()
        messages.success(request, 'تم حذف المسمى الوظيفي بنجاح.')
        return redirect('employment:position_list')
    return render(request, 'employment/position_confirm_delete.html', {'position': position})


# صفة التعيين
@login_required
def appointment_type_list(request):
    """View for listing appointment types"""
    appointment_types = AppointmentType.objects.annotate(employee_count=Count('employees', filter=Q(employees__is_current=True)))
    return render(request, 'employment/appointment_type_list.html', {'appointment_types': appointment_types})


@login_required
def appointment_type_create(request):
    """View for creating a new appointment type"""
    if request.method == 'POST':
        form = AppointmentTypeForm(request.POST)
        if form.is_valid():
            appointment_type = form.save()
            messages.success(request, 'تم إضافة صفة التعيين بنجاح.')
            return redirect('employment:appointment_type_detail', pk=appointment_type.pk)
    else:
        form = AppointmentTypeForm()
    return render(request, 'employment/appointment_type_form.html', {'form': form})


@login_required
def appointment_type_detail(request, pk):
    """View for showing appointment type details"""
    appointment_type = get_object_or_404(AppointmentType, pk=pk)
    employees = Employment.objects.filter(appointment_type=appointment_type, is_current=True)
    return render(request, 'employment/appointment_type_detail.html', {
        'appointment_type': appointment_type,
        'employees': employees
    })


@login_required
def appointment_type_update(request, pk):
    """View for updating an appointment type"""
    appointment_type = get_object_or_404(AppointmentType, pk=pk)
    if request.method == 'POST':
        form = AppointmentTypeForm(request.POST, instance=appointment_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث صفة التعيين بنجاح.')
            return redirect('employment:appointment_type_detail', pk=appointment_type.pk)
    else:
        form = AppointmentTypeForm(instance=appointment_type)
    return render(request, 'employment/appointment_type_form.html', {'form': form, 'appointment_type': appointment_type})


@login_required
def appointment_type_delete(request, pk):
    """View for deleting an appointment type"""
    appointment_type = get_object_or_404(AppointmentType, pk=pk)

    # Check if there are employees associated with this appointment type
    employees_count = Employment.objects.filter(appointment_type=appointment_type, is_current=True).count()

    if employees_count > 0:
        messages.error(request, f'لا يمكن حذف صفة التعيين لأنها مرتبطة بـ {employees_count} موظف. قم بتغيير صفة التعيين للموظفين أولاً.')
        return redirect('employment:appointment_type_detail', pk=appointment_type.pk)

    if request.method == 'POST':
        appointment_type.delete()
        messages.success(request, 'تم حذف صفة التعيين بنجاح.')
        return redirect('employment:appointment_type_list')

    return render(request, 'employment/appointment_type_confirm_delete.html', {'appointment_type': appointment_type})


# الحراك الوظيفي
@login_required
def employee_position_list(request):
    """View for listing employee positions"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Filter employee positions
    if search_query:
        employee_positions = EmployeePosition.objects.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(position__name__icontains=search_query)
        ).select_related('employee', 'position').order_by('-date_obtained')
    else:
        employee_positions = EmployeePosition.objects.all().select_related('employee', 'position').order_by('-date_obtained')

    return render(request, 'employment/employee_position_list.html', {
        'employee_positions': employee_positions,
        'search_query': search_query
    })


def remove_added_teacher_positions():
    """Remove teacher positions that were added by the system"""
    teacher_positions_to_remove = [
        'معلم رياضيات',
        'معلم لغة عربية',
        'معلم لغة انجليزية',
        'معلم علوم',
        'معلم تاريخ',
        'معلم جغرافيا',
        'معلم تربية اسلامية',
        'معلم تربية رياضية',
        'معلم حاسوب',
        'معلم فيزياء',
        'معلم كيمياء',
        'معلم احياء',
        'معلم فنون',
        'معلم موسيقى',
        'معلم صف',
    ]

    removed_count = 0
    for position_name in teacher_positions_to_remove:
        try:
            # Check if position exists and has no employee positions assigned
            position = Position.objects.get(name=position_name)
            if not position.employee_positions.exists():
                position.delete()
                removed_count += 1
                print(f"Removed teacher position: {position_name}")
            else:
                print(f"Cannot remove {position_name} - has employee positions assigned")
        except Position.DoesNotExist:
            print(f"Position {position_name} does not exist")

    return removed_count

@login_required
def cleanup_teacher_positions(request):
    """View to cleanup added teacher positions"""
    if request.method == 'POST':
        removed_count = remove_added_teacher_positions()
        if removed_count > 0:
            messages.success(request, f'تم حذف {removed_count} مسمى وظيفي للمعلمين.')
        else:
            messages.info(request, 'لا توجد مسميات وظيفية للحذف أو أنها مرتبطة بموظفين.')
        return redirect('employment:position_list')

    # Show confirmation page
    teacher_positions_to_remove = [
        'معلم رياضيات', 'معلم لغة عربية', 'معلم لغة انجليزية', 'معلم علوم',
        'معلم تاريخ', 'معلم جغرافيا', 'معلم تربية اسلامية', 'معلم تربية رياضية',
        'معلم حاسوب', 'معلم فيزياء', 'معلم كيمياء', 'معلم احياء',
        'معلم فنون', 'معلم موسيقى', 'معلم صف'
    ]

    # Check which positions exist
    existing_positions = []
    for position_name in teacher_positions_to_remove:
        try:
            position = Position.objects.get(name=position_name)
            has_employees = position.employee_positions.exists()
            existing_positions.append({
                'name': position_name,
                'id': position.id,
                'has_employees': has_employees,
                'can_delete': not has_employees
            })
        except Position.DoesNotExist:
            pass

    return render(request, 'employment/cleanup_positions.html', {
        'existing_positions': existing_positions
    })

@login_required
def employee_position_create(request):
    """View for creating a new employee position"""
    if request.method == 'POST':
        # Print request data for debugging
        print("\n" + "=" * 50)
        print("POST data:", request.POST)
        print("Content type:", request.content_type)
        print("Headers:", request.headers)

        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')
        position_id = post_data.get('position')
        date_obtained = post_data.get('date_obtained')

        print("Employee ID:", employee_id)
        print("Ministry Number:", ministry_number)
        print("Position ID:", position_id)
        print("Date Obtained:", date_obtained)
        print("=" * 50 + "\n")

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee'] = str(employee.id)
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'employment/employee_position_form.html', {'form': EmployeePositionForm(post_data)})

        # Create form with modified POST data
        form = EmployeePositionForm(post_data)

        if form.is_valid():
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'employment/employee_position_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'employment/employee_position_form.html', {'form': form})

                # Create a new employee position
                try:
                    # Create the employee position directly
                    position_id = post_data.get('position')
                    date_obtained = post_data.get('date_obtained')
                    notes = post_data.get('notes', '')

                    # Validate required fields
                    if not position_id:
                        messages.error(request, 'الرجاء اختيار المسمى الوظيفي')
                        return render(request, 'employment/employee_position_form.html', {'form': form})

                    if not date_obtained:
                        messages.error(request, 'الرجاء إدخال تاريخ الحصول على المسمى الوظيفي')
                        return render(request, 'employment/employee_position_form.html', {'form': form})

                    # Get position
                    position = Position.objects.get(id=position_id)

                    print(f"Creating employee position with: employee_id={employee.id}, position_id={position_id}, date={date_obtained}")

                    # Create and save the employee position using direct model creation
                    school_level = post_data.get('school_level')

                    # Check if position is a teacher position and school_level is provided
                    is_teacher = position.name.startswith('معلم')

                    employee_position = EmployeePosition.objects.create(
                        employee=employee,
                        position=position,
                        date_obtained=date_obtained,
                        school_level=school_level if is_teacher else None,
                        notes=notes
                    )
                    print("Employee position saved successfully:", employee_position)
                except Exception as e:
                    print("Error saving employee position:", str(e))
                    messages.error(request, f'حدث خطأ أثناء حفظ المسمى الوظيفي: {str(e)}')
                    return render(request, 'employment/employee_position_form.html', {'form': form})

                messages.success(request, 'تم إضافة المسمى الوظيفي للموظف بنجاح.')
                return redirect('employment:employee_position_list')

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ المسمى الوظيفي: {str(e)}')
        else:
            # Display form errors to the user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        form = EmployeePositionForm()

    return render(request, 'employment/employee_position_form.html', {'form': form})


@login_required
def employee_position_update(request, pk):
    """View for updating an employee position"""
    employee_position = get_object_or_404(EmployeePosition, pk=pk)

    if request.method == 'POST':
        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee'] = str(employee.id)
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'employment/employee_position_form.html', {'form': EmployeePositionForm(post_data), 'employee_position': employee_position})

        # Create form with modified POST data
        form = EmployeePositionForm(post_data, instance=employee_position)

        if form.is_valid():
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'employment/employee_position_form.html', {'form': form, 'employee_position': employee_position})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'employment/employee_position_form.html', {'form': form, 'employee_position': employee_position})

                # Update the employee position
                try:
                    # Get form data
                    position_id = post_data.get('position')
                    date_obtained = post_data.get('date_obtained')
                    notes = post_data.get('notes', '')

                    # Validate required fields
                    if not position_id:
                        messages.error(request, 'الرجاء اختيار المسمى الوظيفي')
                        return render(request, 'employment/employee_position_form.html', {'form': form, 'employee_position': employee_position})

                    if not date_obtained:
                        messages.error(request, 'الرجاء إدخال تاريخ الحصول على المسمى الوظيفي')
                        return render(request, 'employment/employee_position_form.html', {'form': form, 'employee_position': employee_position})

                    # Get position
                    position = Position.objects.get(id=position_id)

                    print(f"Updating employee position with: id={employee_position.id}, employee_id={employee.id}, position_id={position_id}, date={date_obtained}")

                    # Update the employee position directly in the database
                    school_level = post_data.get('school_level')

                    # Check if position is a teacher position and school_level is provided
                    is_teacher = position.name.startswith('معلم')

                    EmployeePosition.objects.filter(id=employee_position.id).update(
                        employee=employee,
                        position=position,
                        date_obtained=date_obtained,
                        school_level=school_level if is_teacher else None,
                        notes=notes
                    )

                    # Refresh from database
                    employee_position.refresh_from_db()
                    print("Employee position updated successfully:", employee_position)
                except Exception as e:
                    print("Error updating employee position:", str(e))
                    messages.error(request, f'حدث خطأ أثناء تحديث المسمى الوظيفي: {str(e)}')
                    return render(request, 'employment/employee_position_form.html', {'form': form, 'employee_position': employee_position})

                messages.success(request, 'تم تحديث المسمى الوظيفي للموظف بنجاح.')
                return redirect('employment:employee_position_list')

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث المسمى الوظيفي: {str(e)}')
        else:
            # Display form errors to the user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        # Initialize form with employee data for the template
        form = EmployeePositionForm(instance=employee_position, initial={
            'ministry_number': employee_position.employee.ministry_number,
            'employee_name': employee_position.employee.full_name,
            'employee_id': employee_position.employee.id
        })

    return render(request, 'employment/employee_position_form.html', {'form': form, 'employee_position': employee_position})


@login_required
def employee_position_delete(request, pk):
    """View for deleting an employee position"""
    employee_position = get_object_or_404(EmployeePosition, pk=pk)

    if request.method == 'POST':
        employee_position.delete()
        messages.success(request, 'تم حذف المسمى الوظيفي للموظف بنجاح.')
        return redirect('employment:employee_position_list')

    return render(request, 'employment/employee_position_confirm_delete.html', {'employee_position': employee_position})


@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number - Enhanced search across all sources"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    # Clean the ministry number (remove spaces and special characters)
    ministry_number = ministry_number.strip()

    # Start comprehensive search with multiple strategies

    try:
        employee = None
        search_results = []

        # COMPREHENSIVE SEARCH STRATEGY - Multiple approaches to find the employee

        # Strategy 1: Direct exact match in Employee table
        if not employee:
            try:
                employees = Employee.objects.filter(ministry_number=ministry_number)
                search_results.append(f"🔍 Searching Employee.ministry_number for '{ministry_number}' - Found {employees.count()} matches")
                if employees.exists():
                    employee = employees.first()
                    search_results.append(f"✅ Found in Employee.ministry_number: {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in Employee exact search: {str(e)}")

        # Strategy 2: Direct exact match in EmployeeIdentification table
        if not employee:
            try:
                identifications = EmployeeIdentification.objects.filter(ministry_number=ministry_number).select_related('employee')
                search_results.append(f"🔍 Searching EmployeeIdentification.ministry_number for '{ministry_number}' - Found {identifications.count()} matches")
                if identifications.exists():
                    employee = identifications.first().employee
                    search_results.append(f"✅ Found in EmployeeIdentification.ministry_number: {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in EmployeeIdentification exact search: {str(e)}")

        # Strategy 3: Search in other ID fields in Employee table (allow null/empty values)
        if not employee:
            try:
                # Search in national_id field (including null/empty)
                employees = Employee.objects.filter(national_id=ministry_number)
                search_results.append(f"🔍 Searching Employee.national_id for '{ministry_number}' - Found {employees.count()} matches")
                if employees.exists():
                    employee = employees.first()
                    search_results.append(f"✅ Found in Employee.national_id: {employee.full_name}")
                else:
                    # Search in id_number field if it exists (including null/empty)
                    try:
                        employees = Employee.objects.filter(id_number=ministry_number)
                        search_results.append(f"🔍 Searching Employee.id_number for '{ministry_number}' - Found {employees.count()} matches")
                        if employees.exists():
                            employee = employees.first()
                            search_results.append(f"✅ Found in Employee.id_number: {employee.full_name}")
                    except Exception as inner_e:
                        search_results.append(f"⚠️ Employee.id_number field not available: {str(inner_e)}")
            except Exception as e:
                search_results.append(f"❌ Error in Employee other fields search: {str(e)}")

        # Strategy 4: Search in other ID fields in EmployeeIdentification table
        if not employee:
            try:
                # Search in national_id
                identifications = EmployeeIdentification.objects.filter(national_id=ministry_number).select_related('employee')
                if identifications.exists():
                    employee = identifications.first().employee
                    search_results.append(f"✅ Found in EmployeeIdentification.national_id: {employee.full_name}")
                else:
                    # Search in id_number
                    identifications = EmployeeIdentification.objects.filter(id_number=ministry_number).select_related('employee')
                    if identifications.exists():
                        employee = identifications.first().employee
                        search_results.append(f"✅ Found in EmployeeIdentification.id_number: {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in EmployeeIdentification other fields search: {str(e)}")

        # Strategy 5: Case-insensitive search
        if not employee:
            try:
                # Employee table
                employees = Employee.objects.filter(ministry_number__iexact=ministry_number)
                if employees.exists():
                    employee = employees.first()
                    search_results.append(f"✅ Found in Employee.ministry_number (case-insensitive): {employee.full_name}")
                else:
                    # EmployeeIdentification table
                    identifications = EmployeeIdentification.objects.filter(ministry_number__iexact=ministry_number).select_related('employee')
                    if identifications.exists():
                        employee = identifications.first().employee
                        search_results.append(f"✅ Found in EmployeeIdentification.ministry_number (case-insensitive): {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in case-insensitive search: {str(e)}")

        # Strategy 6: Partial match search (contains)
        if not employee:
            try:
                # Employee table
                employees = Employee.objects.filter(ministry_number__icontains=ministry_number)
                if employees.exists():
                    employee = employees.first()
                    search_results.append(f"✅ Found in Employee.ministry_number (partial): {employee.full_name}")
                else:
                    # EmployeeIdentification table
                    identifications = EmployeeIdentification.objects.filter(ministry_number__icontains=ministry_number).select_related('employee')
                    if identifications.exists():
                        employee = identifications.first().employee
                        search_results.append(f"✅ Found in EmployeeIdentification.ministry_number (partial): {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in partial match search: {str(e)}")

        # Strategy 7: Search through Employment relationships
        if not employee:
            try:
                # Through Employee
                employments = Employment.objects.filter(
                    employee__ministry_number=ministry_number
                ).select_related('employee', 'department')
                if employments.exists():
                    employee = employments.first().employee
                    department = employments.first().department
                    search_results.append(f"✅ Found through Employment in {department.name}: {employee.full_name}")
                else:
                    # Through EmployeeIdentification
                    employments = Employment.objects.filter(
                        employee__identifications__ministry_number=ministry_number
                    ).select_related('employee', 'department')
                    if employments.exists():
                        employee = employments.first().employee
                        department = employments.first().department
                        search_results.append(f"✅ Found through Employment+Identification in {department.name}: {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in Employment search: {str(e)}")

        # Strategy 8: Comprehensive Q object search
        if not employee:
            try:
                # Search all possible fields with Q objects
                employees = Employee.objects.filter(
                    Q(ministry_number=ministry_number) |
                    Q(ministry_number__icontains=ministry_number) |
                    Q(id_number=ministry_number) |
                    Q(id_number__icontains=ministry_number)
                )
                if employees.exists():
                    employee = employees.first()
                    search_results.append(f"✅ Found in Employee (Q search): {employee.full_name}")
                else:
                    identifications = EmployeeIdentification.objects.filter(
                        Q(ministry_number=ministry_number) |
                        Q(ministry_number__icontains=ministry_number) |
                        Q(national_id=ministry_number) |
                        Q(national_id__icontains=ministry_number) |
                        Q(id_number=ministry_number) |
                        Q(id_number__icontains=ministry_number)
                    ).select_related('employee')
                    if identifications.exists():
                        employee = identifications.first().employee
                        search_results.append(f"✅ Found in EmployeeIdentification (Q search): {employee.full_name}")
            except Exception as e:
                search_results.append(f"❌ Error in Q object search: {str(e)}")

        # Strategy 9: Digit-only matching (last resort)
        if not employee:
            try:
                clean_number = ''.join(filter(str.isdigit, ministry_number))
                if clean_number:
                    search_results.append(f"🔍 Trying digit-only search for: {clean_number}")

                    # Search in Employee table
                    all_employees = Employee.objects.exclude(ministry_number__isnull=True).exclude(ministry_number='')
                    for emp in all_employees:
                        emp_clean = ''.join(filter(str.isdigit, str(emp.ministry_number)))
                        if clean_number == emp_clean:
                            employee = emp
                            search_results.append(f"✅ Found by digit matching in Employee: {employee.full_name} ({emp.ministry_number})")
                            break

                    # Search in EmployeeIdentification table
                    if not employee:
                        all_identifications = EmployeeIdentification.objects.exclude(ministry_number__isnull=True).exclude(ministry_number='').select_related('employee')
                        for ident in all_identifications:
                            ident_clean = ''.join(filter(str.isdigit, str(ident.ministry_number)))
                            if clean_number == ident_clean:
                                employee = ident.employee
                                search_results.append(f"✅ Found by digit matching in EmployeeIdentification: {employee.full_name} ({ident.ministry_number})")
                                break

                    # Also search in ALL employees (including those without ministry numbers)
                    if not employee:
                        search_results.append(f"🔍 Searching in ALL employees for digit pattern: {clean_number}")
                        all_employees_including_empty = Employee.objects.all()
                        for emp in all_employees_including_empty:
                            # Check all possible ID fields
                            fields_to_check = []
                            if hasattr(emp, 'ministry_number') and emp.ministry_number:
                                fields_to_check.append(('ministry_number', emp.ministry_number))
                            if hasattr(emp, 'national_id') and emp.national_id:
                                fields_to_check.append(('national_id', emp.national_id))
                            if hasattr(emp, 'id_number') and emp.id_number:
                                fields_to_check.append(('id_number', emp.id_number))

                            for field_name, field_value in fields_to_check:
                                field_clean = ''.join(filter(str.isdigit, str(field_value)))
                                if clean_number == field_clean:
                                    employee = emp
                                    search_results.append(f"✅ Found by digit matching in Employee.{field_name}: {employee.full_name} ({field_value})")
                                    break

                            if employee:
                                break
                else:
                    search_results.append(f"⚠️ No digits found in search term: {ministry_number}")
            except Exception as e:
                search_results.append(f"❌ Error in digit matching search: {str(e)}")

        # Strategy 10: Emergency search - check if the number might be in a different format
        if not employee:
            try:
                search_results.append(f"🚨 Emergency search - checking all possible formats")

                # Try with leading zeros
                if ministry_number.isdigit():
                    padded_numbers = [
                        ministry_number.zfill(6),  # 6 digits with leading zeros
                        ministry_number.zfill(8),  # 8 digits with leading zeros
                        ministry_number.zfill(10), # 10 digits with leading zeros
                    ]

                    for padded in padded_numbers:
                        employees = Employee.objects.filter(
                            Q(ministry_number=padded) |
                            Q(ministry_number__icontains=padded) |
                            Q(national_id=padded) |
                            Q(national_id__icontains=padded)
                        )
                        if employees.exists():
                            employee = employees.first()
                            search_results.append(f"✅ Found with padded format {padded}: {employee.full_name}")
                            break

                        # Also check in EmployeeIdentification
                        if not employee:
                            identifications = EmployeeIdentification.objects.filter(
                                Q(ministry_number=padded) |
                                Q(ministry_number__icontains=padded) |
                                Q(national_id=padded) |
                                Q(national_id__icontains=padded)
                            ).select_related('employee')
                            if identifications.exists():
                                employee = identifications.first().employee
                                search_results.append(f"✅ Found with padded format {padded} in EmployeeIdentification: {employee.full_name}")
                                break

                # Try removing leading zeros
                if not employee and ministry_number.startswith('0'):
                    stripped_number = ministry_number.lstrip('0')
                    if stripped_number:
                        employees = Employee.objects.filter(
                            Q(ministry_number=stripped_number) |
                            Q(ministry_number__icontains=stripped_number) |
                            Q(national_id=stripped_number) |
                            Q(national_id__icontains=stripped_number)
                        )
                        if employees.exists():
                            employee = employees.first()
                            search_results.append(f"✅ Found with stripped zeros {stripped_number}: {employee.full_name}")

                        # Also check in EmployeeIdentification
                        if not employee:
                            identifications = EmployeeIdentification.objects.filter(
                                Q(ministry_number=stripped_number) |
                                Q(ministry_number__icontains=stripped_number) |
                                Q(national_id=stripped_number) |
                                Q(national_id__icontains=stripped_number)
                            ).select_related('employee')
                            if identifications.exists():
                                employee = identifications.first().employee
                                search_results.append(f"✅ Found with stripped zeros {stripped_number} in EmployeeIdentification: {employee.full_name}")

            except Exception as e:
                search_results.append(f"❌ Error in emergency search: {str(e)}")

        # Strategy 11: Search by name if the input might be a name (for employees without ID numbers)
        if not employee:
            try:
                search_results.append(f"🔍 Strategy 11 - Searching by name pattern for employees without ID numbers")

                # Check if the search term might be a name (contains Arabic letters or spaces)
                has_arabic = any('\u0600' <= char <= '\u06FF' for char in ministry_number)
                has_spaces = ' ' in ministry_number

                if has_arabic or has_spaces or len(ministry_number) > 10:
                    search_results.append(f"🔍 Search term appears to be a name: '{ministry_number}'")

                    # Search by full name
                    employees = Employee.objects.filter(full_name__icontains=ministry_number)
                    search_results.append(f"🔍 Searching Employee.full_name for '{ministry_number}' - Found {employees.count()} matches")

                    if employees.exists():
                        employee = employees.first()
                        search_results.append(f"✅ Found by name search: {employee.full_name}")
                    else:
                        # Try partial name search
                        name_parts = ministry_number.split()
                        if len(name_parts) > 1:
                            for part in name_parts:
                                if len(part) > 2:  # Only search for meaningful parts
                                    employees = Employee.objects.filter(full_name__icontains=part)
                                    if employees.exists():
                                        employee = employees.first()
                                        search_results.append(f"✅ Found by partial name search ('{part}'): {employee.full_name}")
                                        break
                else:
                    # For numeric inputs, also search employees without ministry numbers
                    search_results.append(f"🔍 Searching employees without ministry numbers")
                    employees_without_ministry = Employee.objects.filter(
                        Q(ministry_number__isnull=True) | Q(ministry_number='')
                    )
                    search_results.append(f"🔍 Found {employees_without_ministry.count()} employees without ministry numbers")

                    # Check if any of these employees have the search term in other fields
                    for emp in employees_without_ministry:
                        # Check national_id
                        if hasattr(emp, 'national_id') and emp.national_id and ministry_number in str(emp.national_id):
                            employee = emp
                            search_results.append(f"✅ Found employee without ministry number by national_id: {employee.full_name}")
                            break

                        # Check if they have identification records
                        try:
                            identifications = emp.identifications.all()
                            for ident in identifications:
                                if (ident.ministry_number and ministry_number in str(ident.ministry_number)) or \
                                   (ident.national_id and ministry_number in str(ident.national_id)) or \
                                   (ident.id_number and ministry_number in str(ident.id_number)):
                                    employee = emp
                                    search_results.append(f"✅ Found employee without ministry number through identification: {employee.full_name}")
                                    break
                            if employee:
                                break
                        except:
                            pass

            except Exception as e:
                search_results.append(f"❌ Error in name/no-ID search: {str(e)}")

        # Final result processing
        if employee:
            # Get the best ministry number to display
            display_ministry_number = ministry_number  # Use the searched number

            # Try to get from identifications first
            try:
                identification = employee.identifications.first()
                if identification and identification.ministry_number:
                    display_ministry_number = identification.ministry_number
                elif employee.ministry_number:
                    display_ministry_number = employee.ministry_number
            except:
                if employee.ministry_number:
                    display_ministry_number = employee.ministry_number

            return JsonResponse({
                'success': True,
                'employee': {
                    'id': employee.id,
                    'full_name': employee.full_name,
                    'ministry_number': display_ministry_number
                },
                'debug_info': search_results
            })
        else:
            # Provide comprehensive debug information
            total_employees = Employee.objects.count()
            total_identifications = EmployeeIdentification.objects.count()
            total_employments = Employment.objects.count()

            # Get comprehensive sample data for debugging
            sample_employees = Employee.objects.exclude(ministry_number__isnull=True).exclude(ministry_number='')[:10]
            sample_identifications = EmployeeIdentification.objects.exclude(ministry_number__isnull=True).exclude(ministry_number='')[:10]

            # Also get recent employees (likely to be new)
            recent_employees = Employee.objects.order_by('-id')[:10]

            # Get all employees with ministry numbers containing the searched digits
            clean_search = ''.join(filter(str.isdigit, ministry_number))
            similar_employees = []
            if clean_search:
                for emp in Employee.objects.all():
                    if emp.ministry_number:
                        emp_clean = ''.join(filter(str.isdigit, emp.ministry_number))
                        if clean_search in emp_clean or emp_clean in clean_search:
                            similar_employees.append(f"{emp.full_name}: {emp.ministry_number}")
                            if len(similar_employees) >= 5:
                                break

            debug_info = {
                'search_results': search_results,
                'total_employees': total_employees,
                'total_identifications': total_identifications,
                'total_employments': total_employments,
                'searched_number': ministry_number,
                'clean_searched_number': clean_search,
                'sample_employee_numbers': [f"{emp.full_name}: {emp.ministry_number}" for emp in sample_employees],
                'sample_identification_numbers': [f"{ident.employee.full_name}: {ident.ministry_number}" for ident in sample_identifications],
                'recent_employees': [f"{emp.full_name}: {emp.ministry_number}" for emp in recent_employees],
                'similar_employees': similar_employees
            }

            return JsonResponse({
                'success': False,
                'error': f'لم يتم العثور على موظف بالرقم الوزاري: {ministry_number}',
                'debug_info': debug_info
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في البحث: {str(e)}',
            'debug_info': {
                'search_results': search_results if 'search_results' in locals() else [],
                'exception': str(e),
                'searched_number': ministry_number
            }
        })


# شهادة الخبرة
@login_required
def experience_certificate_list(request):
    """View for listing employees for experience certificate"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Filter employees
    if search_query:
        employees = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(full_name__icontains=search_query)
        ).order_by('full_name')
    else:
        employees = Employee.objects.all().order_by('full_name')

    return render(request, 'employment/experience_certificate_list.html', {
        'employees': employees,
        'search_query': search_query
    })


@login_required
def experience_certificate_view(request, pk):
    """View for displaying an employee's experience certificate"""
    employee = get_object_or_404(Employee, pk=pk)

    # Get employee positions ordered by date
    positions = list(employee.positions.all().order_by('date_obtained'))

    # Get unpaid leaves
    unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
    unpaid_leaves = []
    if unpaid_leave_type:
        unpaid_leaves = Leave.objects.filter(
            employee=employee,
            leave_type=unpaid_leave_type,
            status='approved'
        ).order_by('start_date')

    # Add today's date to context
    today = date.today()

    # Calculate total unpaid leave days
    total_unpaid_days = sum(leave.days_count for leave in unpaid_leaves)

    # Create a combined list of positions and leaves for chronological display
    timeline_items = []

    # Add positions to timeline
    for position in positions:
        timeline_items.append({
            'type': 'position',
            'date': position.date_obtained,
            'position': position,
            'description': position.position.name
        })

    # Add unpaid leaves to timeline
    for leave in unpaid_leaves:
        timeline_items.append({
            'type': 'leave_start',
            'date': leave.start_date,
            'leave': leave,
            'description': 'بداية إجازة بدون راتب'
        })
        timeline_items.append({
            'type': 'leave_end',
            'date': leave.end_date,
            'leave': leave,
            'description': 'نهاية إجازة بدون راتب'
        })

    # Sort timeline by date
    timeline_items.sort(key=lambda x: x['date'])

    # Process timeline to create rows for the certificate table
    certificate_rows = []
    current_position = None
    row_counter = 1

    for i, item in enumerate(timeline_items):
        if item['type'] == 'position':
            current_position = item['position']
            # Find the end date (next position, leave start, or today)
            end_date = None
            for next_item in timeline_items[i+1:]:  # Look at future items
                if next_item['type'] in ['position', 'leave_start']:
                    end_date = next_item['date']
                    break

            if not end_date:  # If no future position or leave, use today
                end_date = today

            # Check if position is a teacher position
            is_teacher = current_position.position.name.startswith('معلم')
            school_level = current_position.school_level if is_teacher and current_position.school_level else None

            certificate_rows.append({
                'row_num': row_counter,
                'type': 'position',
                'position': current_position,
                'start_date': item['date'],
                'end_date': end_date,
                'description': item['description'],
                'is_teacher': is_teacher,
                'school_level': school_level
            })
            row_counter += 1

        elif item['type'] == 'leave_start':
            # Add a row for the leave
            certificate_rows.append({
                'row_num': row_counter,
                'type': 'leave',
                'leave': item['leave'],
                'start_date': item['date'],
                'end_date': item['leave'].end_date,
                'description': 'إجازة بدون راتب'
            })
            row_counter += 1

            # If there's a current position, we need to add a new row for it after the leave
            if current_position and i < len(timeline_items) - 1:
                # Find the next item after leave_end
                for j, next_item in enumerate(timeline_items[i+1:]):
                    if next_item['type'] == 'leave_end' and next_item['leave'] == item['leave']:
                        # Look for the next position or leave_start after this leave_end
                        end_date = None
                        for future_item in timeline_items[i+j+2:]:  # Items after leave_end
                            if future_item['type'] in ['position', 'leave_start']:
                                end_date = future_item['date']
                                break

                        if not end_date:  # If no future position or leave, use today
                            end_date = today

                        # Add a new row for continuing the position after leave
                        # Check if position is a teacher position
                        is_teacher = current_position.position.name.startswith('معلم')
                        school_level = current_position.school_level if is_teacher and current_position.school_level else None

                        certificate_rows.append({
                            'row_num': row_counter,
                            'type': 'position',
                            'position': current_position,
                            'start_date': next_item['date'] + timedelta(days=1),  # Day after leave ends
                            'end_date': end_date,
                            'description': current_position.position.name,
                            'is_teacher': is_teacher,
                            'school_level': school_level
                        })
                        row_counter += 1
                        break

    return render(request, 'employment/experience_certificate_view.html', {
        'employee': employee,
        'positions': positions,
        'today': today,
        'unpaid_leaves': unpaid_leaves,
        'total_unpaid_days': total_unpaid_days,
        'certificate_rows': certificate_rows
    })


@login_required
def experience_certificate_export(request, pk):
    """View for exporting an employee's experience certificate as Word document"""
    employee = get_object_or_404(Employee, pk=pk)

    # Check if docx module is available
    if docx is None:
        messages.error(request, 'مكتبة python-docx غير متوفرة. يرجى تثبيتها أولاً.')
        return redirect('employees:employee_detail', pk=pk)

    # Get employee positions ordered by date
    positions = employee.positions.all().order_by('date_obtained')

    # Get unpaid leaves
    unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
    unpaid_leaves = []
    if unpaid_leave_type:
        unpaid_leaves = Leave.objects.filter(
            employee=employee,
            leave_type=unpaid_leave_type,
            status='approved'
        ).order_by('start_date')

    # Calculate total unpaid leave days
    total_unpaid_days = sum(leave.days_count for leave in unpaid_leaves)

    # Create a combined list of positions and leaves for chronological display
    timeline_items = []

    # Add positions to timeline
    for position in positions:
        timeline_items.append({
            'type': 'position',
            'date': position.date_obtained,
            'position': position,
            'description': position.position.name
        })

    # Add unpaid leaves to timeline
    for leave in unpaid_leaves:
        timeline_items.append({
            'type': 'leave_start',
            'date': leave.start_date,
            'leave': leave,
            'description': 'بداية إجازة بدون راتب'
        })
        timeline_items.append({
            'type': 'leave_end',
            'date': leave.end_date,
            'leave': leave,
            'description': 'نهاية إجازة بدون راتب'
        })

    # Sort timeline by date
    timeline_items.sort(key=lambda x: x['date'])

    # Process timeline to create rows for the certificate table
    certificate_rows = []
    current_position = None
    row_counter = 1

    for i, item in enumerate(timeline_items):
        if item['type'] == 'position':
            current_position = item['position']
            # Find the end date (next position, leave start, or today)
            end_date = None
            for next_item in timeline_items[i+1:]:  # Look at future items
                if next_item['type'] in ['position', 'leave_start']:
                    end_date = next_item['date']
                    break

            if not end_date:  # If no future position or leave, use today
                end_date = date.today()

            # Check if position is a teacher position
            is_teacher = current_position.position.name.startswith('معلم')
            school_level = current_position.school_level if is_teacher and current_position.school_level else None

            certificate_rows.append({
                'row_num': row_counter,
                'type': 'position',
                'position': current_position,
                'start_date': item['date'],
                'end_date': end_date,
                'description': item['description'],
                'is_teacher': is_teacher,
                'school_level': school_level
            })
            row_counter += 1

        elif item['type'] == 'leave_start':
            # Add a row for the leave
            certificate_rows.append({
                'row_num': row_counter,
                'type': 'leave',
                'leave': item['leave'],
                'start_date': item['date'],
                'end_date': item['leave'].end_date,
                'description': 'إجازة بدون راتب'
            })
            row_counter += 1

            # If there's a current position, we need to add a new row for it after the leave
            if current_position and i < len(timeline_items) - 1:
                # Find the next item after leave_end
                for j, next_item in enumerate(timeline_items[i+1:]):
                    if next_item['type'] == 'leave_end' and next_item['leave'] == item['leave']:
                        # Look for the next position or leave_start after this leave_end
                        end_date = None
                        for future_item in timeline_items[i+j+2:]:  # Items after leave_end
                            if future_item['type'] in ['position', 'leave_start']:
                                end_date = future_item['date']
                                break

                        if not end_date:  # If no future position or leave, use today
                            end_date = date.today()

                        # Add a new row for continuing the position after leave
                        # Check if position is a teacher position
                        is_teacher = current_position.position.name.startswith('معلم')
                        school_level = current_position.school_level if is_teacher and current_position.school_level else None

                        certificate_rows.append({
                            'row_num': row_counter,
                            'type': 'position',
                            'position': current_position,
                            'start_date': next_item['date'] + timedelta(days=1),  # Day after leave ends
                            'end_date': end_date,
                            'description': current_position.position.name,
                            'is_teacher': is_teacher,
                            'school_level': school_level
                        })
                        row_counter += 1
                        break

    # Create a new Word document
    doc = docx.Document()

    # Set RTL direction for the entire document
    from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
    from docx.enum.table import WD_TABLE_ALIGNMENT
    from docx.shared import Pt, Mm, Inches, RGBColor
    from docx.oxml.ns import qn

    # Set document properties
    doc.core_properties.title = f"شهادة خبرة - {employee.full_name}"
    doc.core_properties.author = "مديرية التربية والتعليم / لواء قصبة المفرق"

    # Set page margins to 0.5cm
    section = doc.sections[0]
    section.page_width = Mm(210)  # A4 width
    section.page_height = Mm(297)  # A4 height
    section.left_margin = Mm(5)   # 0.5cm left margin
    section.right_margin = Mm(5)  # 0.5cm right margin
    section.top_margin = Mm(5)    # 0.5cm top margin
    section.bottom_margin = Mm(5) # 0.5cm bottom margin

    # Set RTL direction for paragraphs and runs instead of document level

    # Add heading
    heading1 = doc.add_paragraph()
    heading1.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    heading1_run = heading1.add_run("وزارة التربية والتعليم")
    heading1_run.bold = True
    heading1_run.font.size = Pt(16)
    heading1_run.font.rtl = True

    # Add space
    doc.add_paragraph()

    heading2 = doc.add_paragraph()
    heading2.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    heading2_run = heading2.add_run("مديرية التربية والتعليم / لواء قصبة المفرق")
    heading2_run.bold = True
    heading2_run.font.size = Pt(14)
    heading2_run.font.rtl = True

    # Add space
    doc.add_paragraph()

    # Add title
    title = doc.add_paragraph()
    title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title_run = title.add_run("لمن يهمه الأمر")
    title_run.bold = True
    title_run.font.size = Pt(14)
    title_run.font.rtl = True

    # Add space
    doc.add_paragraph()

    # Add greeting
    greeting = doc.add_paragraph()
    greeting.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    greeting_run = greeting.add_run("السلام عليكم ورحمة الله وبركاته:")
    greeting_run.bold = True
    greeting_run.font.size = Pt(12)
    greeting_run.font.rtl = True

    # Add employee information
    info1 = doc.add_paragraph()
    info1.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    info1_run = info1.add_run(f"تشير سجلات هذه المديرية بأن السيد / {employee.full_name}")
    info1_run.font.size = Pt(12)
    info1_run.font.rtl = True

    info2 = doc.add_paragraph()
    info2.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    info2_run = info2.add_run(f"الرقم الوطني: {employee.national_id}")
    info2_run.font.size = Pt(12)
    info2_run.font.rtl = True

    info3 = doc.add_paragraph()
    info3.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    info3_run = info3.add_run(f"المؤهل العلمي والتخصص: {employee.qualification} / {employee.specialization}")
    info3_run.font.size = Pt(12)
    info3_run.font.rtl = True

    info4 = doc.add_paragraph()
    info4.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    info4_run = info4.add_run(f"يعمل في ملاك وزارة التربية والتعليم اعتبارا من تاريخ {employee.hire_date.strftime('%Y-%m-%d')}")
    info4_run.font.size = Pt(12)
    info4_run.font.rtl = True

    info5 = doc.add_paragraph()
    info5.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    info5_run = info5.add_run("ولا يزال على رأس عمله حتى تاريخه وعلى النحو التالي:")
    info5_run.font.size = Pt(12)
    info5_run.font.rtl = True

    # Add table with proper structure
    table = doc.add_table(rows=3 + (len(certificate_rows) if certificate_rows else 1), cols=10)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Set table width to 19cm
    table.autofit = False
    table.width = Mm(190)  # 19cm

    # Set column widths (percentage of table width)
    # Reduce the width of the position column (column 1) and increase the date columns
    col_widths = [5, 15, 15, 15, 6, 6, 6, 6, 6, 6]  # Percentages - adjusted for better date columns
    for i, width_pct in enumerate(col_widths):
        for cell in table.columns[i].cells:
            cell.width = Mm(190 * width_pct / 100)

    # Header row 1
    header_row1 = table.rows[0].cells
    header_row1[0].text = "الرقم"
    header_row1[1].text = "الوظيفة"
    header_row1[2].text = "المبحث الذي يدرسه"
    header_row1[3].text = "المرحلة التي يدرسها"
    header_row1[4].text = "الفترة الزمنية"

    # Merge cells for "الفترة الزمنية"
    header_row1[4].merge(header_row1[5])
    header_row1[4].merge(header_row1[6])
    header_row1[4].merge(header_row1[7])
    header_row1[4].merge(header_row1[8])
    header_row1[4].merge(header_row1[9])

    # Header row 2
    header_row2 = table.rows[1].cells
    header_row2[4].text = "من"
    header_row2[7].text = "إلى"

    # Merge cells for "من" and "إلى"
    header_row2[4].merge(header_row2[5])
    header_row2[4].merge(header_row2[6])
    header_row2[7].merge(header_row2[8])
    header_row2[7].merge(header_row2[9])

    # Header row 3
    header_row3 = table.rows[2].cells
    header_row3[4].text = "اليوم"
    header_row3[5].text = "الشهر"
    header_row3[6].text = "السنة"
    header_row3[7].text = "اليوم"
    header_row3[8].text = "الشهر"
    header_row3[9].text = "السنة"

    # Format header cells
    for row_idx in range(3):
        for cell in table.rows[row_idx].cells:
            if cell.text:  # Only process cells with text
                cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                for run in cell.paragraphs[0].runs:
                    run.font.size = Pt(11)
                    run.font.bold = True
                    run.font.rtl = True

    # Reverse column order for RTL direction
    # We need to modify the table structure to make it RTL
    # First, let's create a function to set RTL properties for a table
    def set_table_rtl(table):
        # Try to set the table's bidiVisual property to make it RTL
        try:
            from docx.oxml.ns import nsdecls
            tblPr = table._element.xpath('./w:tblPr')[0]
            bidiVisual = tblPr.xpath('./w:bidiVisual')
            if not bidiVisual:
                bidiVisual = docx.oxml.parse_xml(f'<w:bidiVisual {nsdecls("w")} w:val="1"/>')
                tblPr.append(bidiVisual)
        except:
            # If we can't set the RTL property directly, we'll handle it through cell content
            pass

    # Apply RTL to the table
    set_table_rtl(table)

    # Add data rows from certificate_rows
    if certificate_rows:
        for i, row in enumerate(certificate_rows):
            row_cells = table.rows[i + 3].cells

            # Fill cells in reverse order for RTL effect
            # Column 0 (rightmost in RTL) - Row number
            row_cells[0].text = str(row['row_num'])

            if row['type'] == 'position':
                # Column 1 - Position description
                row_cells[1].text = row['description']
                # Column 2 - Specialization
                row_cells[2].text = employee.specialization

                # Column 3 - School level for teachers
                if 'is_teacher' in row and row['is_teacher'] and 'school_level' in row and row['school_level']:
                    if row['school_level'] == 'primary':
                        row_cells[3].text = 'اساسي'
                    elif row['school_level'] == 'secondary':
                        row_cells[3].text = 'ثانوي'
                    elif row['school_level'] == 'both':
                        row_cells[3].text = 'اساسي + ثانوي'
                    else:
                        row_cells[3].text = "-"
                else:
                    row_cells[3].text = "-"
            elif row['type'] == 'leave':
                row_cells[1].text = row['description']
                row_cells[2].text = "-"
                row_cells[3].text = "-"

            # Date columns - from
            row_cells[4].text = row['start_date'].strftime('%d')
            row_cells[5].text = row['start_date'].strftime('%m')
            row_cells[6].text = row['start_date'].strftime('%Y')

            # Date columns - to
            row_cells[7].text = row['end_date'].strftime('%d')
            row_cells[8].text = row['end_date'].strftime('%m')
            row_cells[9].text = row['end_date'].strftime('%Y')

            # Format data cells
            for cell in row_cells:
                # Set paragraph alignment to center
                cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

                # Make sure we have at least one run
                if not cell.paragraphs[0].runs:
                    cell.paragraphs[0].add_run(cell.text)
                    cell.text = ""

                # Set RTL for all runs
                for run in cell.paragraphs[0].runs:
                    run.font.size = Pt(11)
                    run.font.rtl = True

                # Set paragraph direction to RTL
                try:
                    from docx.oxml import OxmlElement
                    para = cell.paragraphs[0]._p
                    pPr = para.get_or_add_pPr()
                    bidi = pPr.get_or_add_bidi()
                    bidi.val = True
                except:
                    # If we can't set the RTL property directly, we'll rely on font.rtl
                    pass
    else:
        # If no rows, add a single row with hire date to today
        row_cells = table.rows[3].cells
        row_cells[0].text = "1"
        row_cells[1].text = employee.get_latest_position()
        row_cells[2].text = employee.specialization
        row_cells[3].text = "-"
        row_cells[4].text = employee.hire_date.strftime('%d')
        row_cells[5].text = employee.hire_date.strftime('%m')
        row_cells[6].text = employee.hire_date.strftime('%Y')
        # Add today's date
        today = date.today()
        row_cells[7].text = today.strftime('%d')
        row_cells[8].text = today.strftime('%m')
        row_cells[9].text = today.strftime('%Y')

        # Format data cells
        for cell in row_cells:
            cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

            # Make sure we have at least one run
            if not cell.paragraphs[0].runs:
                cell.paragraphs[0].add_run(cell.text)
                cell.text = ""

            # Set RTL for all runs
            for run in cell.paragraphs[0].runs:
                run.font.size = Pt(11)
                run.font.rtl = True

            # Set paragraph direction to RTL
            try:
                from docx.oxml import OxmlElement
                para = cell.paragraphs[0]._p
                pPr = para.get_or_add_pPr()
                bidi = pPr.get_or_add_bidi()
                bidi.val = True
            except:
                # If we can't set the RTL property directly, we'll rely on font.rtl
                pass

    # Add closing text
    closing1 = doc.add_paragraph()
    closing1.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    closing1_run = closing1.add_run("قد أعطيت هذه الوثيقة بناء على طلبه لتقديمها .")
    closing1_run.font.size = Pt(12)
    closing1_run.font.rtl = True

    # Add signature
    signature = doc.add_paragraph()
    signature.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    signature_run = signature.add_run("مدير التربية والتعليم")
    signature_run.bold = True
    signature_run.font.size = Pt(14)
    signature_run.font.rtl = True

    # Add empty paragraphs for spacing
    for _ in range(5):
        doc.add_paragraph()

    # Add copy note
    copy_note = doc.add_paragraph()
    copy_note.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    copy_note_run = copy_note.add_run("نسخة / الملف الشخصي")
    copy_note_run.font.size = Pt(11)
    copy_note_run.font.rtl = True

    # Add empty paragraphs for spacing
    for _ in range(4):
        doc.add_paragraph()

    # Add form number at the bottom right
    form_number = doc.add_paragraph()
    form_number.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
    form_number_run = form_number.add_run("Form # QF72-1-22 rev.c")
    form_number_run.font.size = Pt(8)
    form_number_run.font.color.rgb = RGBColor(100, 100, 100)

    # Create response with the document
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    response['Content-Disposition'] = f'attachment; filename="شهادة_خبرة_{employee.ministry_number}.docx"'

    # Save the document to the response
    doc.save(response)

    return response


# البيانات التعريفية للموظف
@login_required
def employee_identification_list(request):
    """View for listing employee identification data"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Get existing identification records
    existing_ids = EmployeeIdentification.objects.values_list('employee_id', flat=True)

    # Filter identifications
    if search_query:
        # First, get existing identification records
        identifications = EmployeeIdentification.objects.filter(
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(id_number__icontains=search_query)
        ).select_related('employee')

        # Then, get employees without identification records
        employees_without_id = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(national_id__icontains=search_query)
        ).exclude(id__in=existing_ids)
    else:
        # Get all existing identification records
        identifications = EmployeeIdentification.objects.all().select_related('employee')

        # Get all employees without identification records
        employees_without_id = Employee.objects.exclude(id__in=existing_ids)

    # Create a list to hold all employee data (both with and without identification records)
    employee_data = []

    # Add existing identification records
    for identification in identifications:
        birth_date_str = ""
        if identification.birth_day and identification.birth_month and identification.birth_year:
            birth_date_str = f"{identification.birth_year}-{identification.birth_month:02d}-{identification.birth_day:02d}"

        employee_data.append({
            'has_identification': True,
            'identification': identification,
            'employee': identification.employee,
            'ministry_number': identification.ministry_number,
            'national_id': identification.national_id,
            'id_number': identification.id_number,
            'birth_date': birth_date_str,
            'address': identification.address
        })

    # Add employees without identification records
    for employee in employees_without_id:
        # Format birth_date as string if it exists
        birth_date_str = ""
        if employee.birth_date:
            birth_date_str = employee.birth_date.strftime("%Y-%m-%d")

        employee_data.append({
            'has_identification': False,
            'identification': None,
            'employee': employee,
            'ministry_number': employee.ministry_number,
            'national_id': employee.national_id,
            'id_number': "-",
            'birth_date': birth_date_str,
            'address': employee.address
        })

    # Sort the combined list by employee name
    employee_data.sort(key=lambda x: x['employee'].full_name)

    return render(request, 'employment/employee_identification_list.html', {
        'employee_data': employee_data,
        'search_query': search_query
    })


@login_required
def employee_identification_create(request):
    """View for creating a new employee identification record"""
    # Get ministry number from query parameter if available
    ministry_number = request.GET.get('ministry_number', '')
    employee = None

    # If we have a ministry number, try to get the employee
    if ministry_number:
        try:
            employee = Employee.objects.get(ministry_number=ministry_number)
        except Employee.DoesNotExist:
            messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')

    if request.method == 'POST':
        form = EmployeeIdentificationForm(request.POST)
        if form.is_valid():
            identification = form.save()
            messages.success(request, 'تم إضافة البيانات التعريفية للموظف بنجاح.')
            return redirect('employment:employee_identification_detail', pk=identification.pk)
    else:
        # If we have an employee, pre-populate the form
        initial_data = {}
        if employee:
            initial_data = {
                'employee': employee,
                'employee_id': employee.id,
                'ministry_number': ministry_number,
                'employee_name': employee.full_name,
                'national_id': employee.national_id,
                'address': employee.address
            }

            # If employee has birth_date, extract components
            if employee.birth_date:
                initial_data['birth_day'] = employee.birth_date.day
                initial_data['birth_month'] = employee.birth_date.month
                initial_data['birth_year'] = employee.birth_date.year

        form = EmployeeIdentificationForm(initial=initial_data)

    return render(request, 'employment/employee_identification_form.html', {
        'form': form,
        'title': 'إضافة بيانات تعريفية جديدة',
        'ministry_number': ministry_number
    })


@login_required
def employee_identification_detail(request, pk):
    """View for viewing an employee identification record"""
    identification = get_object_or_404(EmployeeIdentification, pk=pk)
    return render(request, 'employment/employee_identification_detail.html', {
        'identification': identification
    })


@login_required
def employee_identification_update(request, pk):
    """View for updating an employee identification record"""
    identification = get_object_or_404(EmployeeIdentification, pk=pk)

    if request.method == 'POST':
        form = EmployeeIdentificationForm(request.POST, instance=identification)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث البيانات التعريفية للموظف بنجاح.')
            return redirect('employment:employee_identification_detail', pk=identification.pk)
    else:
        form = EmployeeIdentificationForm(instance=identification)

    return render(request, 'employment/employee_identification_form.html', {
        'form': form,
        'identification': identification,
        'title': 'تعديل البيانات التعريفية للموظف'
    })


@login_required
def employee_identification_delete(request, pk):
    """View for deleting an employee identification record"""
    identification = get_object_or_404(EmployeeIdentification, pk=pk)

    if request.method == 'POST':
        identification.delete()
        messages.success(request, 'تم حذف البيانات التعريفية للموظف بنجاح.')
        return redirect('employment:employee_identification_list')

    return render(request, 'employment/employee_identification_confirm_delete.html', {
        'identification': identification
    })


@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee data by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Prepare birth date data if available
        birth_date = None
        if employee.birth_date:
            birth_date = {
                'day': employee.birth_date.day,
                'month': employee.birth_date.month,
                'year': employee.birth_date.year,
            }

        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number,
                'national_id': employee.national_id,
                'birth_date': birth_date,
                'address': employee.address,
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})


@login_required
def update_id_number(request):
    """AJAX view to update ID number for an employee identification"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    identification_id = request.POST.get('identification_id')
    id_number = request.POST.get('id_number')

    if not identification_id or not id_number:
        return JsonResponse({'success': False, 'error': 'البيانات المطلوبة غير مكتملة'})

    try:
        identification = EmployeeIdentification.objects.get(pk=identification_id)

        # Check if ID number already exists for another employee
        existing = EmployeeIdentification.objects.filter(id_number=id_number).exclude(pk=identification_id).first()
        if existing:
            return JsonResponse({
                'success': False,
                'error': f'رقم الهوية {id_number} مستخدم بالفعل للموظف {existing.employee.full_name}'
            })

        identification.id_number = id_number
        identification.save(update_fields=['id_number'])

        return JsonResponse({
            'success': True,
            'message': 'تم تحديث رقم الهوية بنجاح'
        })
    except EmployeeIdentification.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على بيانات التعريف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@login_required
def delete_id_number_ajax(request):
    """AJAX view to delete ID number for an employee identification"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    identification_id = request.POST.get('identification_id')

    if not identification_id:
        return JsonResponse({'success': False, 'error': 'البيانات المطلوبة غير مكتملة'})

    try:
        identification = EmployeeIdentification.objects.get(pk=identification_id)

        # حفظ معلومات الموظف للرسالة
        employee_name = identification.employee.full_name

        # حذف رقم الهوية فقط (وليس السجل بأكمله)
        identification.id_number = ''
        identification.save(update_fields=['id_number'])

        return JsonResponse({
            'success': True,
            'message': f'تم حذف رقم الهوية للموظف {employee_name} بنجاح'
        })
    except EmployeeIdentification.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على بيانات التعريف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@login_required
def edit_id_number(request, pk):
    """View for editing only the ID number of an employee identification"""
    identification = get_object_or_404(EmployeeIdentification, pk=pk)
    error = None

    if request.method == 'POST':
        id_number = request.POST.get('id_number', '').strip()

        if not id_number:
            error = 'الرجاء إدخال رقم الهوية'
        else:
            # Check if ID number already exists for another employee
            existing = EmployeeIdentification.objects.filter(id_number=id_number).exclude(pk=pk).first()
            if existing:
                error = f'رقم الهوية {id_number} مستخدم بالفعل للموظف {existing.employee.full_name}'
            else:
                # Update the ID number
                identification.id_number = id_number
                identification.save(update_fields=['id_number'])
                messages.success(request, 'تم تحديث رقم الهوية بنجاح')
                return redirect('employment:employee_identification_list')

    return render(request, 'employment/edit_id_number.html', {
        'identification': identification,
        'error': error
    })


@login_required
def delete_id_number(request, pk):
    """View for deleting only the ID number of an employee identification"""
    identification = get_object_or_404(EmployeeIdentification, pk=pk)

    if request.method == 'POST':
        # Delete only the ID number (not the entire record)
        identification.id_number = ''
        identification.save(update_fields=['id_number'])
        messages.success(request, f'تم حذف رقم الهوية للموظف {identification.employee.full_name} بنجاح')
        return redirect('employment:employee_identification_list')

    return render(request, 'employment/delete_id_number.html', {
        'identification': identification
    })


@login_required
def add_id_number(request):
    """AJAX view to add ID number for an employee without identification data"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    ministry_number = request.POST.get('ministry_number')
    id_number = request.POST.get('id_number')

    if not ministry_number or not id_number:
        return JsonResponse({'success': False, 'error': 'البيانات المطلوبة غير مكتملة'})

    try:
        # Check if ID number already exists
        existing = EmployeeIdentification.objects.filter(id_number=id_number).first()
        if existing:
            return JsonResponse({
                'success': False,
                'error': f'رقم الهوية {id_number} مستخدم بالفعل للموظف {existing.employee.full_name}'
            })

        # Get the employee
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Check if employee already has identification data
        existing_identification = EmployeeIdentification.objects.filter(employee=employee).first()
        if existing_identification:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} لديه بالفعل بيانات تعريفية. يرجى تحديث البيانات الحالية بدلاً من إضافة بيانات جديدة.'
            })

        # Create minimal identification record
        identification = EmployeeIdentification(
            employee=employee,
            ministry_number=ministry_number,
            national_id=employee.national_id or '',
            id_number=id_number,
            birth_day=employee.birth_date.day if employee.birth_date else 1,
            birth_month=employee.birth_date.month if employee.birth_date else 1,
            birth_year=employee.birth_date.year if employee.birth_date else 2000,
            address=employee.address or ''
        )
        identification.save()

        return JsonResponse({
            'success': True,
            'message': 'تم إضافة رقم الهوية بنجاح',
            'identification_id': identification.pk
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على الموظف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@login_required
def add_id_number_page(request):
    """View for adding ID number through a dedicated page"""
    ministry_number = request.GET.get('ministry_number')
    error_message = None
    employee = None

    # Get employee data
    if ministry_number:
        try:
            employee = Employee.objects.get(ministry_number=ministry_number)
        except Employee.DoesNotExist:
            error_message = f'لم يتم العثور على موظف بالرقم الوزاري {ministry_number}'
    else:
        error_message = 'الرجاء تحديد الرقم الوزاري للموظف'

    # Handle form submission
    if request.method == 'POST' and employee:
        id_number = request.POST.get('id_number')

        if id_number:
            try:
                # Check if ID number already exists for another employee
                existing_id = EmployeeIdentification.objects.filter(id_number=id_number).first()
                if existing_id and existing_id.employee != employee:
                    messages.error(request, f'رقم الهوية {id_number} مستخدم بالفعل للموظف {existing_id.employee.full_name}')
                    return render(request, 'employment/add_id_number.html', {
                        'employee': employee,
                        'error_message': error_message
                    })

                # Check if employee already has identification data
                existing_identification = EmployeeIdentification.objects.filter(employee=employee).first()

                if existing_identification:
                    # Update existing identification
                    existing_identification.id_number = id_number
                    existing_identification.save(update_fields=['id_number'])
                    messages.success(request, 'تم تحديث رقم الهوية بنجاح')
                else:
                    # Create new identification
                    identification = EmployeeIdentification(
                        employee=employee,
                        ministry_number=ministry_number,
                        national_id=employee.national_id or '',
                        id_number=id_number,
                        birth_day=employee.birth_date.day if employee.birth_date else 1,
                        birth_month=employee.birth_date.month if employee.birth_date else 1,
                        birth_year=employee.birth_date.year if employee.birth_date else 2000,
                        address=employee.address or ''
                    )
                    identification.save()
                    messages.success(request, 'تم إضافة رقم الهوية بنجاح')

                return redirect('employment:employee_identification_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ البيانات: {str(e)}')
        else:
            messages.error(request, 'الرجاء إدخال رقم الهوية')

    return render(request, 'employment/add_id_number.html', {
        'employee': employee,
        'error_message': error_message
    })


@login_required
def export_identifications_pdf(request):
    """View for exporting employee identifications as PDF with proper Arabic support using arabic_reshaper and bidi"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Get existing identification records
    existing_ids = EmployeeIdentification.objects.values_list('employee_id', flat=True)

    # Filter identifications
    if search_query:
        # First, get existing identification records
        identifications = EmployeeIdentification.objects.filter(
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(id_number__icontains=search_query)
        ).select_related('employee')

        # Then, get employees without identification records
        employees_without_id = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(national_id__icontains=search_query)
        ).exclude(id__in=existing_ids)
    else:
        # Get all existing identification records
        identifications = EmployeeIdentification.objects.all().select_related('employee')

        # Get all employees without identification records
        employees_without_id = Employee.objects.exclude(id__in=existing_ids)

    # Create a list to hold all employee data (both with and without identification records)
    employee_data = []

    # Add existing identification records
    for identification in identifications:
        employee_data.append({
            'has_identification': True,
            'identification': identification,
            'employee': identification.employee,
            'ministry_number': identification.ministry_number,
            'national_id': identification.national_id,
            'id_number': identification.id_number,
            'birth_date': f"{identification.birth_day}/{identification.birth_month}/{identification.birth_year}" if identification.birth_day else "",
            'address': identification.address
        })

    # Add employees without identification records
    for employee in employees_without_id:
        # Extract birth date components if available
        birth_date = ""
        if employee.birth_date:
            birth_date = employee.birth_date.strftime("%d/%m/%Y")

        employee_data.append({
            'has_identification': False,
            'identification': None,
            'employee': employee,
            'ministry_number': employee.ministry_number,
            'national_id': employee.national_id,
            'id_number': "-",
            'birth_date': birth_date,
            'address': employee.address
        })

    # Sort the combined list by employee name
    employee_data.sort(key=lambda x: x['employee'].full_name)

    # Create a response object with PDF content type
    response = HttpResponse(content_type='application/pdf; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="employee_identifications.pdf"'

    # Register Arabic fonts - we'll register multiple fonts for better compatibility
    fonts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'fonts')

    # Register Noto Sans Arabic font
    noto_sans_path = os.path.join(fonts_dir, 'NotoSansArabic-Regular.ttf')
    pdfmetrics.registerFont(TTFont('NotoSansArabic', noto_sans_path))

    # Register Arial font as fallback
    arial_path = os.path.join(fonts_dir, 'arial.ttf')
    if os.path.exists(arial_path):
        pdfmetrics.registerFont(TTFont('Arial', arial_path))

    # Create the PDF document with RTL support and landscape orientation for wider table
    doc = SimpleDocTemplate(
        response,
        pagesize=landscape(A4),  # Use landscape orientation for wider table
        rightMargin=30,  # Reduced margins to fit more content
        leftMargin=30,
        topMargin=30,
        bottomMargin=50,  # Keep enough space for the date at bottom
        encoding='UTF-8'  # Explicitly set UTF-8 encoding
    )

    # Create styles
    styles = getSampleStyleSheet()

    # Create a custom style for Arabic text with improved settings
    arabic_style = ParagraphStyle(
        'ArabicStyle',
        parent=styles['Normal'],
        fontName='NotoSansArabic',
        fontSize=9,  # Reduced font size to fit in one line
        alignment=1,  # Center alignment
        leading=11,  # Reduced line height
        spaceAfter=0,  # Reduced space after
        firstLineIndent=0,
        rightIndent=0,
        leftIndent=0,
        wordWrap='CJK',  # Prevent word wrapping for better single-line display
        splitLongWords=0  # Prevent splitting long words
    )

    arabic_header_style = ParagraphStyle(
        'ArabicHeaderStyle',
        parent=styles['Heading1'],
        fontName='NotoSansArabic',
        fontSize=18,
        alignment=1,  # Center alignment
        leading=24,
        spaceAfter=12,
        textColor=colors.darkblue
    )

    # Define light beige color for table headers (light sandy color)
    light_beige = colors.Color(0.94, 0.90, 0.80)

    # Helper function to process Arabic text
    def process_arabic_text(text):
        if not text:
            return ""
        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        # Apply bidirectional algorithm
        bidi_text = get_display(reshaped_text)
        return bidi_text

    # Create the content elements
    elements = []

    # Add title
    title_text = process_arabic_text('البيانات التعريفية للموظفين')
    title = Paragraph(title_text, arabic_header_style)
    elements.append(title)
    elements.append(Spacer(1, 20))

    # Get the data from the table in the UI
    # Prepare table headers with processed Arabic text - in RTL order
    headers = [
        process_arabic_text('العنوان'),
        process_arabic_text('تاريخ الميلاد'),
        process_arabic_text('رقم الهوية'),
        process_arabic_text('الرقم الوطني'),
        process_arabic_text('اسم الموظف'),
        process_arabic_text('الرقم الوزاري'),
    ]

    # Create table data with processed headers
    data = [[Paragraph(header, arabic_style) for header in headers]]

    # Add data rows with processed Arabic text
    for item in employee_data:
        # Format birth date as numbers only
        birth_date = item['birth_date'] or ""

        # Process each text field
        ministry_number = process_arabic_text(item['ministry_number'])
        full_name = process_arabic_text(item['employee'].full_name)
        national_id = process_arabic_text(item['national_id'] or "")
        id_number = process_arabic_text(item['id_number'] or "")
        birth_date_processed = process_arabic_text(birth_date)
        address = process_arabic_text(item['address'] or "")

        # Add row in RTL order
        row = [
            Paragraph(address, arabic_style),
            Paragraph(birth_date_processed, arabic_style),
            Paragraph(id_number, arabic_style),
            Paragraph(national_id, arabic_style),
            Paragraph(full_name, arabic_style),
            Paragraph(ministry_number, arabic_style),
        ]
        data.append(row)

    # Create the table with optimized column widths for single-line display in landscape mode
    # Wider columns for text that might be longer, taking advantage of landscape orientation
    col_widths = [180, 70, 90, 90, 160, 70]  # Optimized for landscape orientation and single-line content
    table = Table(data, repeatRows=1, colWidths=col_widths)

    # Style the table with improved settings for Arabic
    table_style = TableStyle([
        # Header row styling - changed to light beige color
        ('BACKGROUND', (0, 0), (-1, 0), light_beige),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),  # Changed to black for better readability on light background
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (-1, 0), 'NotoSansArabic'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),  # Reduced header font size
        ('BOTTOMPADDING', (0, 0), (-1, 0), 6),  # Reduced padding
        ('TOPPADDING', (0, 0), (-1, 0), 6),  # Reduced padding

        # Data rows styling
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('FONTNAME', (0, 1), (-1, -1), 'NotoSansArabic'),
        ('FONTSIZE', (0, 1), (-1, -1), 9),  # Reduced font size for data rows
        ('BOTTOMPADDING', (0, 1), (-1, -1), 4),  # Reduced padding
        ('TOPPADDING', (0, 1), (-1, -1), 4),  # Reduced padding

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOX', (0, 0), (-1, -1), 2, colors.black),
        ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),

        # Padding
        ('RIGHTPADDING', (0, 0), (-1, -1), 12),
        ('LEFTPADDING', (0, 0), (-1, -1), 12),
    ])

    # Add zebra striping for better readability
    for i in range(1, len(data)):
        if i % 2 == 0:
            table_style.add('BACKGROUND', (0, i), (-1, i), colors.lightgrey)

    table.setStyle(table_style)

    # Add the table to the elements
    elements.append(table)

    # Add footer with page numbers and date
    def add_page_number_and_date(canvas, doc):
        canvas.saveState()
        canvas.setFont('NotoSansArabic', 10)

        # Add page number
        page_num = canvas.getPageNumber()
        page_text = process_arabic_text(f"صفحة {page_num}")
        canvas.drawRightString(540, 30, page_text)

        # Add current date at the bottom of the page
        today = date.today()
        formatted_date = f"{today.day}/{today.month}/{today.year}"
        date_text = process_arabic_text(f"تاريخ التقرير: {formatted_date}")
        canvas.drawCentredString(doc.pagesize[0]/2, 30, date_text)

        canvas.restoreState()

    # Build the PDF document with page numbers and date
    doc.build(elements, onFirstPage=add_page_number_and_date, onLaterPages=add_page_number_and_date)

    return response


@login_required
def export_identifications_excel(request):
    """View for exporting employee identifications as Excel"""
    # Get search parameter
    search_query = request.GET.get('search', '')

    # Get existing identification records
    existing_ids = EmployeeIdentification.objects.values_list('employee_id', flat=True)

    # Filter identifications
    if search_query:
        # First, get existing identification records
        identifications = EmployeeIdentification.objects.filter(
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(id_number__icontains=search_query)
        ).select_related('employee')

        # Then, get employees without identification records
        employees_without_id = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(national_id__icontains=search_query)
        ).exclude(id__in=existing_ids)
    else:
        # Get all existing identification records
        identifications = EmployeeIdentification.objects.all().select_related('employee')

        # Get all employees without identification records
        employees_without_id = Employee.objects.exclude(id__in=existing_ids)

    # Create a list to hold all employee data (both with and without identification records)
    employee_data = []

    # Add existing identification records
    for identification in identifications:
        employee_data.append({
            'has_identification': True,
            'employee': identification.employee,
            'ministry_number': identification.ministry_number,
            'full_name': identification.employee.full_name,
            'national_id': identification.national_id,
            'id_number': identification.id_number,
            'birth_date': f"{identification.birth_day}/{identification.birth_month}/{identification.birth_year}" if identification.birth_day else "",
            'address': identification.address
        })

    # Add employees without identification records
    for employee in employees_without_id:
        # Extract birth date components if available
        birth_date = ""
        if employee.birth_date:
            birth_date = employee.birth_date.strftime("%d/%m/%Y")

        employee_data.append({
            'has_identification': False,
            'employee': employee,
            'ministry_number': employee.ministry_number,
            'full_name': employee.full_name,
            'national_id': employee.national_id,
            'id_number': "-",
            'birth_date': birth_date,
            'address': employee.address
        })

    # Sort the combined list by employee name
    employee_data.sort(key=lambda x: x['employee'].full_name)

    # Create a pandas DataFrame from the employee data
    df = pd.DataFrame([
        {
            'الرقم الوزاري': item['ministry_number'],
            'اسم الموظف': item['full_name'],
            'الرقم الوطني': item['national_id'] or '',
            'رقم الهوية': item['id_number'] or '',
            'تاريخ الميلاد': item['birth_date'] or '',
            'العنوان': item['address'] or ''
        }
        for item in employee_data
    ])

    # Create a response with Excel content type
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="employee_identifications.xlsx"'

    # Create Excel file in memory
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='البيانات التعريفية للموظفين')

        # Auto-adjust columns' width
        worksheet = writer.sheets['البيانات التعريفية للموظفين']
        for idx, col in enumerate(df.columns):
            # Find the maximum length of the data in each column
            max_len = max(
                df[col].astype(str).map(len).max(),  # max length of the column data
                len(str(col))  # length of the column name
            ) + 2  # adding a little extra space

            # Set the column width
            worksheet.column_dimensions[chr(65 + idx)].width = max_len

    return response


@login_required
def import_id_numbers(request):
    """View for importing ID numbers from Excel file"""
    if request.method == 'POST':
        form = IdNumberImportForm(request.POST, request.FILES)
        if form.is_valid():
            # Process the Excel file
            df = form.df

            # Initialize counters
            success_count = 0
            error_count = 0
            errors = []

            # Process each row
            for index, row in df.iterrows():
                try:
                    ministry_number = str(row['الرقم الوزاري']).strip()
                    id_number = str(row['رقم الهوية']).strip()

                    # Skip empty rows
                    if not ministry_number or not id_number:
                        continue

                    # Get the employee
                    try:
                        employee = Employee.objects.get(ministry_number=ministry_number)
                    except Employee.DoesNotExist:
                        errors.append(f"الموظف برقم وزاري {ministry_number} غير موجود")
                        error_count += 1
                        continue

                    # Check if employee already has identification data
                    existing_identification = EmployeeIdentification.objects.filter(employee=employee).first()

                    if existing_identification:
                        # Update existing identification
                        existing_identification.id_number = id_number
                        existing_identification.save(update_fields=['id_number'])
                    else:
                        # Create new identification
                        identification = EmployeeIdentification(
                            employee=employee,
                            ministry_number=ministry_number,
                            national_id=employee.national_id or '',
                            id_number=id_number,
                            birth_day=employee.birth_date.day if employee.birth_date else 1,
                            birth_month=employee.birth_date.month if employee.birth_date else 1,
                            birth_year=employee.birth_date.year if employee.birth_date else 2000,
                            address=employee.address or ''
                        )
                        identification.save()

                    success_count += 1

                except Exception as e:
                    errors.append(f"خطأ في السطر {index + 2}: {str(e)}")
                    error_count += 1

            # Show success message
            if success_count > 0:
                messages.success(request, f'تم استيراد {success_count} رقم هوية بنجاح.')

            # Show error message if any
            if error_count > 0:
                error_message = f'فشل استيراد {error_count} سجل. '
                if errors:
                    error_message += f'الأخطاء: {", ".join(errors[:5])}'
                    if len(errors) > 5:
                        error_message += f' و{len(errors) - 5} أخطاء أخرى.'
                messages.error(request, error_message)

            return redirect('employment:employee_identification_list')
    else:
        form = IdNumberImportForm()

    return render(request, 'employment/import_id_numbers.html', {
        'form': form,
        'title': 'استيراد أرقام الهوية من ملف إكسل'
    })


@login_required
def test_form(request):
    """Test form view for debugging"""
    if request.method == 'POST':
        print("\n" + "=" * 50)
        print("TEST FORM POST data:", request.POST)

        # Get form data
        ministry_number = request.POST.get('ministry_number')
        position_id = request.POST.get('position')
        date_obtained = request.POST.get('date_obtained')
        notes = request.POST.get('notes', '')

        print(f"Ministry Number: {ministry_number}")
        print(f"Position ID: {position_id}")
        print(f"Date Obtained: {date_obtained}")
        print(f"Notes: {notes}")

        # Validate data
        if not ministry_number:
            messages.error(request, 'الرجاء إدخال الرقم الوزاري')
            return redirect('employment:test_form')

        if not position_id:
            messages.error(request, 'الرجاء اختيار المسمى الوظيفي')
            return redirect('employment:test_form')

        if not date_obtained:
            messages.error(request, 'الرجاء إدخال تاريخ الحصول على المسمى الوظيفي')
            return redirect('employment:test_form')

        # Get employee
        try:
            employee = Employee.objects.get(ministry_number=ministry_number)
            print(f"Found employee: {employee}")
        except Employee.DoesNotExist:
            messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
            return redirect('employment:test_form')

        # Get position
        try:
            position = Position.objects.get(id=position_id)
            print(f"Found position: {position}")
        except Position.DoesNotExist:
            messages.error(request, 'لم يتم العثور على المسمى الوظيفي')
            return redirect('employment:test_form')

        # Create employee position
        try:
            employee_position = EmployeePosition(
                employee=employee,
                position=position,
                date_obtained=date_obtained,
                notes=notes
            )
            employee_position.save()
            print(f"Created employee position: {employee_position}")
            messages.success(request, 'تم إضافة المسمى الوظيفي للموظف بنجاح')
            return redirect('employment:employee_position_list')
        except Exception as e:
            print(f"Error creating employee position: {str(e)}")
            messages.error(request, f'حدث خطأ أثناء حفظ المسمى الوظيفي: {str(e)}')
            return redirect('employment:test_form')

    positions = Position.objects.all()
    return render(request, 'employment/test_form.html', {'positions': positions})


@login_required
def excess_employee_list(request):
    """View for listing excess employees"""
    # Get departments that have excess employees
    departments_with_excess = Department.objects.filter(
        excess_employees__isnull=False  # Using the related_name from the ExcessEmployee model
    ).distinct().order_by('name')

    # If no departments with excess employees are found, fallback to all school departments
    if not departments_with_excess.exists():
        departments_with_excess = Department.objects.filter(workplace='school').order_by('name')

    # Initialize search form with departments that have excess employees
    search_form = ExcessEmployeeSearchForm(request.GET)
    search_form.fields['department'].queryset = departments_with_excess

    # Get filter parameters
    search_term = request.GET.get('search_term', '')
    department_id = request.GET.get('department', '')
    status = request.GET.get('status', '')
    export_excel = request.GET.get('export_excel', '')

    # Base queryset
    excess_employees = ExcessEmployee.objects.all()

    # Apply filters
    if search_term:
        excess_employees = excess_employees.filter(
            Q(employee__ministry_number__icontains=search_term) |
            Q(employee__full_name__icontains=search_term)
        )

    if department_id:
        excess_employees = excess_employees.filter(current_department_id=department_id)

    if status:
        excess_employees = excess_employees.filter(status=status)

    # Order by creation date (newest first)
    excess_employees = excess_employees.order_by('-created_at')

    # Get statistics
    pending_count = excess_employees.filter(status='pending').count()
    resolved_count = excess_employees.filter(status='resolved').count()
    # إجمالي الموظفين الزوائد = الموظفين قيد المعالجة فقط (وليس الكل)
    total_count = pending_count

    # Get count of departments with excess employees
    departments_count = Department.objects.filter(
        id__in=ExcessEmployee.objects.filter(status='pending').values_list('current_department', flat=True)
    ).distinct().count()

    # Handle Excel export
    if export_excel == '1':
        # Redirect to the dedicated export function with the same filters
        url = reverse('employment:export_excess_employees_excel')
        params = {}
        if search_term:
            params['search_term'] = search_term
        if department_id:
            params['department'] = department_id
        if status:
            params['status'] = status

        if params:
            url = f"{url}?{urlencode(params)}"

        return redirect(url)

    return render(request, 'employment/excess_employee_list.html', {
        'excess_employees': excess_employees,
        'search_form': search_form,
        'total_count': total_count,
        'pending_count': pending_count,
        'resolved_count': resolved_count,
        'departments_count': departments_count,
        'departments': departments_with_excess
    })


@login_required
def excess_employee_create(request):
    """View for creating a new excess employee record"""
    if request.method == 'POST':
        # Print POST data for debugging
        print("POST data:", request.POST)

        # Create a new form instance with the POST data
        form = ExcessEmployeeForm(request.POST)

        # Check if the form is valid
        if form.is_valid():
            try:
                # Get the employee
                employee = form.cleaned_data['employee']

                # Get current department and position
                # First try to get from employment
                current_employment = Employment.objects.filter(employee=employee).first()

                # Create a new ExcessEmployee instance
                excess_employee = form.save(commit=False)

                # Set department and position from current employment or school
                if current_employment and current_employment.department:
                    excess_employee.current_department = current_employment.department
                    excess_employee.position = current_employment.position
                elif employee.school:
                    excess_employee.current_department = employee.school

                    # Get latest position
                    latest_position = employee.positions.order_by('-date_obtained').first()
                    if latest_position:
                        excess_employee.position = latest_position.position

                # Save the excess employee record
                excess_employee.save()

                messages.success(request, 'تم إضافة الموظف الزائد بنجاح.')
                return redirect('employment:excess_employee_detail', pk=excess_employee.pk)
            except Exception as e:
                # If an error occurs during saving, show an error message
                print(f"Error saving form: {str(e)}")
                messages.error(request, f'حدث خطأ أثناء حفظ البيانات: {str(e)}')
        else:
            # If the form is not valid, show error messages
            print("Form errors:", form.errors)
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'خطأ في حقل {field}: {error}')
    else:
        # If it's a GET request, create an empty form
        form = ExcessEmployeeForm()

    # Render the template with the form
    return render(request, 'employment/excess_employee_form.html', {
        'form': form,
        'title': 'إضافة موظف زائد'
    })


@login_required
def excess_employee_detail(request, pk):
    """View for viewing excess employee details"""
    excess_employee = get_object_or_404(ExcessEmployee, pk=pk)

    # Initialize resolve form if needed
    if excess_employee.status == 'pending':
        resolve_form = ExcessEmployeeResolveForm()
    else:
        resolve_form = None

    return render(request, 'employment/excess_employee_detail.html', {
        'excess_employee': excess_employee,
        'resolve_form': resolve_form
    })


@login_required
def excess_employee_update(request, pk):
    """View for updating an excess employee record"""
    excess_employee = get_object_or_404(ExcessEmployee, pk=pk)

    if request.method == 'POST':
        form = ExcessEmployeeForm(request.POST, instance=excess_employee)
        if form.is_valid():
            # Save the form but don't commit yet
            updated_excess_employee = form.save(commit=False)

            # Keep the original department and position
            updated_excess_employee.current_department = excess_employee.current_department
            updated_excess_employee.position = excess_employee.position

            # Now save the updated record
            updated_excess_employee.save()

            messages.success(request, 'تم تحديث بيانات الموظف الزائد بنجاح.')
            return redirect('employment:excess_employee_detail', pk=excess_employee.pk)
    else:
        form = ExcessEmployeeForm(instance=excess_employee)

        # Set the department display field
        if excess_employee.current_department:
            form.fields['department_display'].initial = excess_employee.current_department.name

    return render(request, 'employment/excess_employee_form.html', {
        'form': form,
        'excess_employee': excess_employee,
        'title': 'تعديل بيانات موظف زائد'
    })


@login_required
def excess_employee_delete(request, pk):
    """View for deleting an excess employee record"""
    excess_employee = get_object_or_404(ExcessEmployee, pk=pk)

    if request.method == 'POST':
        excess_employee.delete()
        messages.success(request, 'تم حذف الموظف الزائد بنجاح.')
        return redirect('employment:excess_employee_list')

    return render(request, 'employment/excess_employee_confirm_delete.html', {
        'excess_employee': excess_employee
    })


@login_required
def excess_employee_resolve(request, pk):
    """View for resolving an excess employee issue"""
    excess_employee = get_object_or_404(ExcessEmployee, pk=pk)

    # Check if already resolved
    if excess_employee.status == 'resolved':
        messages.warning(request, 'تم معالجة هذا الموظف الزائد مسبقاً.')
        return redirect('employment:excess_employee_detail', pk=excess_employee.pk)

    if request.method == 'POST':
        form = ExcessEmployeeResolveForm(request.POST)
        if form.is_valid():
            resolution_notes = form.cleaned_data['resolution_notes']

            # Resolve the excess employee
            excess_employee.resolve(resolution_notes)

            # Get statistics after resolving
            pending_count_after = ExcessEmployee.objects.filter(status='pending').count()
            resolved_count_after = ExcessEmployee.objects.filter(status='resolved').count()

            # Show success message with statistics
            messages.success(
                request,
                f'تم معالجة الموظف الزائد بنجاح. اجمالي عدد الموظفين الزوائد: {pending_count_after}، تم تزويب الزوائد: {resolved_count_after}.'
            )

            return redirect('employment:excess_employee_detail', pk=excess_employee.pk)
    else:
        form = ExcessEmployeeResolveForm()

    # Get current statistics for display
    pending_count = ExcessEmployee.objects.filter(status='pending').count()
    resolved_count = ExcessEmployee.objects.filter(status='resolved').count()

    return render(request, 'employment/excess_employee_resolve.html', {
        'excess_employee': excess_employee,
        'form': form,
        'pending_count': pending_count,
        'resolved_count': resolved_count
    })


@login_required
def get_employee_by_ministry_number_json(request):
    """API view to get employee data by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Get latest position
        latest_position = employee.positions.order_by('-date_obtained').first()
        position_name = latest_position.position.name if latest_position else '-'

        # Get current department
        current_employment = Employment.objects.filter(employee=employee).first()
        department_name = None
        department_id = None

        if current_employment and current_employment.department:
            department_name = current_employment.department.name
            department_id = current_employment.department.id

        # Get school name
        school_name = None
        if employee.school:
            try:
                school_name = employee.school.name
            except:
                school_name = str(employee.school)

        # Use department name if available, otherwise use school name
        display_department = department_name or school_name or 'غير محدد'

        # Log for debugging
        print(f"Employee found: {employee.full_name}, School: {school_name}")

        # Use department name if available, otherwise use school name
        display_department = department_name or school_name or 'غير محدد'

        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number,
                'national_id': employee.national_id,
                'position': position_name,
                'position_id': latest_position.position.id if latest_position else None,
                'department': department_name,
                'department_id': department_id,
                'school_name': school_name or 'غير محدد',
                'display_department': display_department,
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
    except Exception as e:
        print(f"Error in get_employee_by_ministry_number_json: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def export_excess_employees_excel(request):
    """View for exporting excess employees to Excel"""
    # Check if pandas is available
    if pd is None:
        messages.error(request, 'مكتبة pandas غير متوفرة. يرجى تثبيتها أولاً.')
        return redirect('employment:excess_employee_list')

    # Get filter parameters from the session or request
    search_term = request.GET.get('search_term', '')
    department_id = request.GET.get('department', '')
    status = request.GET.get('status', '')

    # Base queryset
    excess_employees = ExcessEmployee.objects.all()

    # Apply filters
    if search_term:
        excess_employees = excess_employees.filter(
            Q(employee__ministry_number__icontains=search_term) |
            Q(employee__full_name__icontains=search_term)
        )

    if department_id:
        excess_employees = excess_employees.filter(current_department_id=department_id)

    if status:
        excess_employees = excess_employees.filter(status=status)

    # Order by creation date (newest first)
    excess_employees = excess_employees.order_by('-created_at')

    # Create a DataFrame from the queryset
    data = []
    for ee in excess_employees:
        status_text = 'موظف زائد' if ee.status == 'pending' else 'تم تزويب الزائد'
        data.append({
            'الرقم الوزاري': ee.employee.ministry_number,
            'اسم الموظف': ee.employee.full_name,
            'القسم الحالي': ee.current_department.name,
            'المسمى الوظيفي': ee.position.name,
            'سبب الزيادة': ee.reason,
            'الحالة': status_text,
            'تاريخ الإضافة': ee.created_at.strftime('%Y-%m-%d')
        })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create a response with Excel content type
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="excess_employees.xlsx"'

    # Create Excel file
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='الموظفين الزوائد')

        # Get the worksheet
        worksheet = writer.sheets['الموظفين الزوائد']

        # Set RTL direction
        worksheet.sheet_view.rightToLeft = True

        # Format the columns
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    return response


@login_required
def medical_condition_name_list(request):
    """View for listing medical condition names"""
    condition_names = MedicalConditionName.objects.all().order_by('name')

    return render(request, 'employment/medical_condition_name_list.html', {
        'condition_names': condition_names,
    })


@login_required
def medical_condition_name_create(request):
    """View for creating a new medical condition name"""
    if request.method == 'POST':
        form = MedicalConditionNameForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة اسم الحالة المرضية بنجاح.')
            return redirect('employment:medical_condition_name_list')
    else:
        form = MedicalConditionNameForm()

    return render(request, 'employment/medical_condition_name_form.html', {
        'form': form,
        'title': 'إضافة اسم حالة مرضية جديدة',
    })


@login_required
def medical_condition_name_update(request, pk):
    """View for updating a medical condition name"""
    condition_name = get_object_or_404(MedicalConditionName, pk=pk)

    if request.method == 'POST':
        form = MedicalConditionNameForm(request.POST, instance=condition_name)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث اسم الحالة المرضية بنجاح.')
            return redirect('employment:medical_condition_name_list')
    else:
        form = MedicalConditionNameForm(instance=condition_name)

    return render(request, 'employment/medical_condition_name_form.html', {
        'form': form,
        'condition_name': condition_name,
        'title': 'تعديل اسم الحالة المرضية',
    })


@login_required
def medical_condition_name_delete(request, pk):
    """View for deleting a medical condition name"""
    condition_name = get_object_or_404(MedicalConditionName, pk=pk)

    # Check if the condition name is used in any medical conditions
    if condition_name.medical_conditions.exists():
        messages.error(request, 'لا يمكن حذف اسم الحالة المرضية لأنه مستخدم في حالات مرضية موجودة.')
        return redirect('employment:medical_condition_name_list')

    if request.method == 'POST':
        condition_name.delete()
        messages.success(request, 'تم حذف اسم الحالة المرضية بنجاح.')
        return redirect('employment:medical_condition_name_list')

    return render(request, 'employment/medical_condition_name_confirm_delete.html', {
        'condition_name': condition_name,
    })


@login_required
def medical_condition_list(request):
    """View for listing medical conditions"""
    # Get departments with medical conditions - both from school and employment
    departments_with_conditions = []

    # Get departments from employee's school
    school_departments = Department.objects.filter(
        id__in=MedicalCondition.objects.values_list('employee__school', flat=True).distinct()
    )

    # Get departments from employee's current employment
    employment_departments = Department.objects.filter(
        id__in=Employment.objects.filter(
            employee__in=MedicalCondition.objects.values_list('employee', flat=True).distinct()
        ).values_list('department', flat=True).distinct()
    )

    # Combine both querysets and remove duplicates
    departments_with_conditions = Department.objects.filter(
        Q(id__in=school_departments.values_list('id', flat=True)) |
        Q(id__in=employment_departments.values_list('id', flat=True))
    ).order_by('name')

    # Get positions of employees with medical conditions
    employees_with_conditions = Employee.objects.filter(
        id__in=MedicalCondition.objects.values_list('employee', flat=True).distinct()
    )

    positions_with_conditions = Position.objects.filter(
        id__in=EmployeePosition.objects.filter(
            employee__in=employees_with_conditions
        ).values_list('position', flat=True).distinct()
    ).order_by('name')

    # Initialize search form with filtered querysets
    search_form = MedicalConditionSearchForm(
        request.GET,
        departments_queryset=departments_with_conditions,
        positions_queryset=positions_with_conditions
    )

    # Get filter parameters
    search_term = request.GET.get('search_term', '')
    condition_id = request.GET.get('condition', '')
    department_id = request.GET.get('department', '')
    position_id = request.GET.get('position', '')
    export_excel = request.GET.get('export_excel', '')

    # Base queryset
    medical_conditions = MedicalCondition.objects.all()

    # Apply filters
    if search_term:
        medical_conditions = medical_conditions.filter(
            Q(employee__ministry_number__icontains=search_term) |
            Q(employee__full_name__icontains=search_term) |
            Q(condition__name__icontains=search_term)
        )

    # Filter by condition
    if condition_id:
        medical_conditions = medical_conditions.filter(condition_id=condition_id)

    # Filter by department (either from school or employment)
    if department_id:
        # Get employees in this department (either from school or employment)
        employees_in_department = Employee.objects.filter(
            Q(school_id=department_id) |
            Q(id__in=Employment.objects.filter(department_id=department_id).values_list('employee', flat=True))
        )

        # Filter medical conditions by these employees
        medical_conditions = medical_conditions.filter(employee__in=employees_in_department)

    # Filter by position (employee's latest position)
    if position_id:
        # Get employees with the specified position
        employees_with_position = Employee.objects.filter(
            positions__position_id=position_id
        ).distinct()

        # Filter medical conditions by these employees
        medical_conditions = medical_conditions.filter(employee__in=employees_with_position)

    # Order by medical report date (newest first)
    medical_conditions = medical_conditions.order_by('-medical_report_date')

    # Get total count
    total_count = medical_conditions.count()

    # Get count of departments with medical conditions
    departments_count = medical_conditions.values('employee__school').distinct().count()

    # Handle Excel export
    if export_excel == '1':
        # Redirect to the dedicated export function with the same filters
        url = reverse('employment:export_medical_conditions_excel')
        params = {}
        if search_term:
            params['search_term'] = search_term
        if condition_id:
            params['condition'] = condition_id
        if department_id:
            params['department'] = department_id
        if position_id:
            params['position'] = position_id

        if params:
            url = f"{url}?{urlencode(params)}"

        return redirect(url)

    return render(request, 'employment/medical_condition_list.html', {
        'medical_conditions': medical_conditions,
        'search_form': search_form,
        'total_count': total_count,
        'departments_count': departments_count,
    })


@login_required
def medical_condition_create(request):
    """View for creating a new medical condition"""
    if request.method == 'POST':
        form = MedicalConditionForm(request.POST)
        if form.is_valid():
            # Check if a medical condition with the same employee already exists
            employee = form.cleaned_data['employee']
            condition = form.cleaned_data['condition']

            # Check for any existing medical condition for this employee
            any_existing_condition = MedicalCondition.objects.filter(
                employee=employee
            ).first()

            if any_existing_condition:
                messages.warning(
                    request,
                    f'يوجد حالة مرضية مسجلة مسبقاً لهذا الموظف. لا يمكن إضافة حالة مرضية جديدة لنفس الموظف.'
                )
                # Redirect to the existing condition detail page
                return redirect('employment:medical_condition_detail', pk=any_existing_condition.pk)

            # Check for existing medical condition with the same name
            existing_condition = MedicalCondition.objects.filter(
                employee=employee,
                condition=condition
            ).first()

            if existing_condition:
                messages.warning(
                    request,
                    f'توجد حالة مرضية مسجلة مسبقاً لهذا الموظف بنفس الاسم.'
                )
                # Redirect to the existing condition detail page
                return redirect('employment:medical_condition_detail', pk=existing_condition.pk)

            # Save the new medical condition if it doesn't exist
            medical_condition = form.save()
            messages.success(request, 'تم إضافة الحالة المرضية بنجاح.')
            return redirect('employment:medical_condition_detail', pk=medical_condition.pk)
    else:
        form = MedicalConditionForm()

    return render(request, 'employment/medical_condition_form.html', {
        'form': form,
        'title': 'إضافة حالة مرضية جديدة',
    })


@login_required
def medical_condition_detail(request, pk):
    """View for viewing a medical condition's details"""
    medical_condition = get_object_or_404(MedicalCondition, pk=pk)

    return render(request, 'employment/medical_condition_detail.html', {
        'medical_condition': medical_condition,
    })


@login_required
def medical_condition_update(request, pk):
    """View for updating a medical condition"""
    medical_condition = get_object_or_404(MedicalCondition, pk=pk)

    if request.method == 'POST':
        form = MedicalConditionForm(request.POST, instance=medical_condition)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الحالة المرضية بنجاح.')
            return redirect('employment:medical_condition_detail', pk=medical_condition.pk)
    else:
        form = MedicalConditionForm(instance=medical_condition)

    return render(request, 'employment/medical_condition_form.html', {
        'form': form,
        'medical_condition': medical_condition,
        'title': 'تعديل الحالة المرضية',
    })


@login_required
def medical_condition_delete(request, pk):
    """View for deleting a medical condition"""
    medical_condition = get_object_or_404(MedicalCondition, pk=pk)

    if request.method == 'POST':
        medical_condition.delete()
        messages.success(request, 'تم حذف الحالة المرضية بنجاح.')
        return redirect('employment:medical_condition_list')

    return render(request, 'employment/medical_condition_confirm_delete.html', {
        'medical_condition': medical_condition,
    })





@login_required
def export_medical_conditions_excel(request):
    """View for exporting medical conditions to Excel"""
    # Check if pandas is available
    if pd is None:
        messages.error(request, 'مكتبة pandas غير متوفرة. يرجى تثبيتها أولاً.')
        return redirect('employment:medical_condition_list')

    # Get filter parameters from the session or request
    search_term = request.GET.get('search_term', '')
    condition_id = request.GET.get('condition', '')
    department_id = request.GET.get('department', '')
    position_id = request.GET.get('position', '')

    # Base queryset
    medical_conditions = MedicalCondition.objects.all()

    # Apply filters
    if search_term:
        medical_conditions = medical_conditions.filter(
            Q(employee__ministry_number__icontains=search_term) |
            Q(employee__full_name__icontains=search_term) |
            Q(condition__name__icontains=search_term)
        )

    # Filter by condition
    if condition_id:
        medical_conditions = medical_conditions.filter(condition_id=condition_id)

    # Filter by department (either from school or employment)
    if department_id:
        # Get employees in this department (either from school or employment)
        employees_in_department = Employee.objects.filter(
            Q(school_id=department_id) |
            Q(id__in=Employment.objects.filter(department_id=department_id).values_list('employee', flat=True))
        )

        # Filter medical conditions by these employees
        medical_conditions = medical_conditions.filter(employee__in=employees_in_department)

    # Filter by position (employee's latest position)
    if position_id:
        # Get employees with the specified position
        employees_with_position = Employee.objects.filter(
            positions__position_id=position_id
        ).distinct()

        # Filter medical conditions by these employees
        medical_conditions = medical_conditions.filter(employee__in=employees_with_position)

    # Order by medical report date (newest first)
    medical_conditions = medical_conditions.order_by('-medical_report_date')

    # Create a DataFrame from the queryset
    data = []
    for mc in medical_conditions:
        # Get department name (either from employment or school)
        current_employment = Employment.objects.filter(employee=mc.employee).first()
        if current_employment and current_employment.department:
            department_name = current_employment.department.name
        elif mc.employee.school:
            department_name = mc.employee.school.name
        else:
            department_name = 'غير محدد'

        # Get latest position
        latest_position = mc.employee.positions.order_by('-date_obtained').first()
        position_name = latest_position.position.name if latest_position else 'غير محدد'

        data.append({
            'الرقم الوزاري': mc.employee.ministry_number,
            'اسم الموظف': mc.employee.full_name,
            'القسم': department_name,
            'المسمى الوظيفي': position_name,
            'الحالة المرضية': mc.condition.name,
            'تاريخ التقرير الطبي': mc.medical_report_date.strftime('%Y-%m-%d'),
            'وصف الحالة': mc.description,
            'ملاحظات': mc.notes or '',
        })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create a response with Excel content type
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="medical_conditions.xlsx"'

    # Create Excel file
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='الحالات المرضية')

        # Get the worksheet
        worksheet = writer.sheets['الحالات المرضية']

        # Set RTL direction
        worksheet.sheet_view.rightToLeft = True

        # Format the columns
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    return response


# BTEC Views
@login_required
def btec_list(request):
    """View for listing BTEC teachers"""
    # Get search parameters
    search_term = request.GET.get('search', '')
    department_filter = request.GET.get('department', '')
    field_filter = request.GET.get('field', '')
    job_filter = request.GET.get('job', '')
    specialization_filter = request.GET.get('specialization', '')

    # Start with all BTEC teachers
    btec_teachers = BtecTeacher.objects.select_related('employee', 'field', 'job').all()

    # Apply filters
    if search_term:
        btec_teachers = btec_teachers.filter(
            Q(employee__full_name__icontains=search_term) |
            Q(employee__identifications__ministry_number__icontains=search_term)
        )

    if field_filter:
        btec_teachers = btec_teachers.filter(field_id=field_filter)

    if job_filter:
        btec_teachers = btec_teachers.filter(job_id=job_filter)

    if specialization_filter:
        btec_teachers = btec_teachers.filter(employee__specialization=specialization_filter)

    # Department filter (based on current employment)
    if department_filter:
        btec_teachers = btec_teachers.filter(
            employee__employments__department_id=department_filter,
            employee__employments__is_current=True
        )

    # Get unique departments from current employments of BTEC teachers
    departments_with_btec = Department.objects.filter(
        employees__employee__btec_records__isnull=False,
        employees__is_current=True
    ).distinct().order_by('name')

    # Get unique specializations from BTEC teachers
    specializations_raw = Employee.objects.filter(
        btec_records__isnull=False
    ).exclude(
        specialization__isnull=True
    ).exclude(
        specialization__exact=''
    ).values_list('specialization', flat=True).distinct().order_by('specialization')

    specializations_choices = [spec for spec in specializations_raw if spec]

    # Calculate statistics
    total_teachers = btec_teachers.count()
    total_departments = departments_with_btec.count()
    total_fields = BtecField.objects.count()
    total_jobs = BtecJob.objects.count()
    total_specializations = len(specializations_choices)

    # Create search form
    from .forms import BtecSearchForm
    search_form = BtecSearchForm(
        request.GET,
        departments_queryset=departments_with_btec,
        specializations_choices=specializations_choices
    )

    # Handle Excel export
    if request.GET.get('export') == 'excel':
        return export_btec_to_excel(btec_teachers)

    return render(request, 'employment/btec_list.html', {
        'btec_teachers': btec_teachers,
        'search_form': search_form,
        'search_term': search_term,
        'department_filter': department_filter,
        'field_filter': field_filter,
        'job_filter': job_filter,
        'specialization_filter': specialization_filter,
        'total_teachers': total_teachers,
        'total_departments': total_departments,
        'total_fields': total_fields,
        'total_jobs': total_jobs,
        'total_specializations': total_specializations,
    })


@login_required
def btec_create(request):
    """View for adding a new BTEC teacher"""
    if request.method == 'POST':
        form = BtecTeacherForm(request.POST)
        if form.is_valid():
            btec_teacher = form.save()
            messages.success(request, f'تم إضافة معلم BTEC {btec_teacher.employee.full_name} بنجاح.')
            return redirect('employment:btec_list')
    else:
        form = BtecTeacherForm()

    return render(request, 'employment/btec_form.html', {
        'form': form,
        'title': 'إضافة معلم BTEC'
    })


@login_required
def btec_update(request, pk):
    """View for updating a BTEC teacher"""
    btec_teacher = get_object_or_404(BtecTeacher, pk=pk)

    if request.method == 'POST':
        form = BtecTeacherForm(request.POST, instance=btec_teacher)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات معلم BTEC {btec_teacher.employee.full_name} بنجاح.')
            return redirect('employment:btec_list')
    else:
        form = BtecTeacherForm(instance=btec_teacher)

    return render(request, 'employment/btec_form.html', {
        'form': form,
        'btec_teacher': btec_teacher,
        'title': 'تحديث معلم BTEC'
    })


@login_required
def btec_delete(request, pk):
    """View for deleting a BTEC teacher"""
    btec_teacher = get_object_or_404(BtecTeacher, pk=pk)

    if request.method == 'POST':
        employee_name = btec_teacher.employee.full_name
        btec_teacher.delete()
        messages.success(request, f'تم حذف معلم BTEC {employee_name} بنجاح.')
        return redirect('employment:btec_list')

    return render(request, 'employment/btec_confirm_delete.html', {
        'btec_teacher': btec_teacher
    })


@login_required
def btec_field_list(request):
    """View for listing BTEC fields"""
    fields = BtecField.objects.annotate(
        teacher_count=Count('teachers')
    ).order_by('name')

    # Calculate total teachers across all fields
    total_teachers = BtecTeacher.objects.count()

    return render(request, 'employment/btec_field_list.html', {
        'fields': fields,
        'total_teachers': total_teachers
    })


@login_required
def btec_field_create(request):
    """View for creating a new BTEC field"""
    if request.method == 'POST':
        form = BtecFieldForm(request.POST)
        if form.is_valid():
            field = form.save()
            messages.success(request, f'تم إضافة حقل BTEC "{field.name}" بنجاح.')
            return redirect('employment:btec_field_list')
    else:
        form = BtecFieldForm()

    return render(request, 'employment/btec_field_form.html', {
        'form': form,
        'title': 'إضافة حقل BTEC جديد'
    })


@login_required
def btec_field_update(request, pk):
    """View for updating a BTEC field"""
    field = get_object_or_404(BtecField, pk=pk)

    if request.method == 'POST':
        form = BtecFieldForm(request.POST, instance=field)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث حقل BTEC "{field.name}" بنجاح.')
            return redirect('employment:btec_field_list')
    else:
        form = BtecFieldForm(instance=field)

    return render(request, 'employment/btec_field_form.html', {
        'form': form,
        'field': field,
        'title': 'تحديث حقل BTEC'
    })


@login_required
def btec_field_delete(request, pk):
    """View for deleting a BTEC field"""
    field = get_object_or_404(BtecField, pk=pk)

    # Check if field has teachers
    teacher_count = field.teachers.count()
    if teacher_count > 0:
        messages.error(request, f'لا يمكن حذف الحقل لأنه مرتبط بـ {teacher_count} معلم. قم بنقل المعلمين إلى حقل آخر أولاً.')
        return redirect('employment:btec_field_list')

    if request.method == 'POST':
        field_name = field.name
        field.delete()
        messages.success(request, f'تم حذف حقل BTEC "{field_name}" بنجاح.')
        return redirect('employment:btec_field_list')

    return render(request, 'employment/btec_field_confirm_delete.html', {
        'field': field
    })


# BTEC Job Views
@login_required
def btec_job_list(request):
    """View for listing BTEC jobs"""
    jobs = BtecJob.objects.annotate(
        teacher_count=Count('teachers')
    ).order_by('name')

    # Calculate total teachers across all jobs
    total_teachers = BtecTeacher.objects.count()

    return render(request, 'employment/btec_job_list.html', {
        'jobs': jobs,
        'total_teachers': total_teachers
    })


@login_required
def btec_job_create(request):
    """View for creating a new BTEC job"""
    if request.method == 'POST':
        form = BtecJobForm(request.POST)
        if form.is_valid():
            job = form.save()
            messages.success(request, f'تم إضافة وظيفة BTEC "{job.name}" بنجاح.')
            return redirect('employment:btec_job_list')
    else:
        form = BtecJobForm()

    return render(request, 'employment/btec_job_form.html', {
        'form': form,
        'title': 'إضافة وظيفة BTEC جديدة'
    })


@login_required
def btec_job_update(request, pk):
    """View for updating a BTEC job"""
    job = get_object_or_404(BtecJob, pk=pk)

    if request.method == 'POST':
        form = BtecJobForm(request.POST, instance=job)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث وظيفة BTEC "{job.name}" بنجاح.')
            return redirect('employment:btec_job_list')
    else:
        form = BtecJobForm(instance=job)

    return render(request, 'employment/btec_job_form.html', {
        'form': form,
        'job': job,
        'title': 'تحديث وظيفة BTEC'
    })


@login_required
def btec_job_delete(request, pk):
    """View for deleting a BTEC job"""
    job = get_object_or_404(BtecJob, pk=pk)

    # Check if job has teachers
    teacher_count = job.teachers.count()
    if teacher_count > 0:
        messages.error(request, f'لا يمكن حذف الوظيفة لأنها مرتبطة بـ {teacher_count} معلم. قم بنقل المعلمين إلى وظيفة أخرى أولاً.')
        return redirect('employment:btec_job_list')

    if request.method == 'POST':
        job_name = job.name
        job.delete()
        messages.success(request, f'تم حذف وظيفة BTEC "{job_name}" بنجاح.')
        return redirect('employment:btec_job_list')

    return render(request, 'employment/btec_job_confirm_delete.html', {
        'job': job
    })


@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number')

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرقم الوزاري مطلوب'})

    try:
        # Try to find employee by ministry number in identifications
        employee = Employee.objects.get(identifications__ministry_number=ministry_number)

        # Get current employment
        current_employment = employee.employments.filter(is_current=True).first()
        department_name = current_employment.department.name if current_employment else '-'

        # Get specialization
        specialization = getattr(employee, 'specialization', '-') or '-'

        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'department': department_name,
                'specialization': specialization
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})


def export_btec_to_excel(btec_teachers):
    """Export BTEC teachers to Excel"""
    # Import HttpResponse at the top to avoid UnboundLocalError
    from django.http import HttpResponse

    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from openpyxl.utils import get_column_letter
    except ImportError:
        response = HttpResponse('مكتبة openpyxl غير متوفرة', content_type='text/plain; charset=utf-8')
        return response

    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "معلمي BTEC"

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    cell_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Headers
    headers = [
        'الرقم',
        'الرقم الوزاري',
        'الاسم الكامل',
        'التخصص',
        'المدرسة',
        'الحقل'
    ]

    # Write headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Write data
    for row, btec_teacher in enumerate(btec_teachers, 2):
        data = [
            row - 1,  # Sequential number
            btec_teacher.ministry_number,
            btec_teacher.employee.full_name,
            btec_teacher.specialization,
            btec_teacher.department_name,
            btec_teacher.field.name
        ]

        for col, value in enumerate(data, 1):
            cell = ws.cell(row=row, column=col, value=value)
            cell.alignment = cell_alignment
            cell.border = border

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)

        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass

        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="btec_teachers.xlsx"'

    wb.save(response)
    return response


# Employee Allowances Views
@login_required
def employee_allowance_list(request):
    """View for listing employee allowances"""
    # Get search parameters
    search_term = request.GET.get('search', '')
    education_allowance = request.GET.get('education_allowance', '')
    adjustment_allowance = request.GET.get('adjustment_allowance', '')
    transportation_allowance = request.GET.get('transportation_allowance', '')
    supervisory_allowance = request.GET.get('supervisory_allowance', '')
    technical_allowance = request.GET.get('technical_allowance', '')

    # Start with all allowances
    allowances = EmployeeAllowance.objects.select_related('employee').all()

    # Apply filters
    if search_term:
        allowances = allowances.filter(
            Q(employee__full_name__icontains=search_term) |
            Q(employee__ministry_number__icontains=search_term)
        )

    if education_allowance:
        allowances = allowances.filter(education_allowance=education_allowance)

    if adjustment_allowance:
        allowances = allowances.filter(adjustment_allowance=adjustment_allowance)

    if transportation_allowance:
        allowances = allowances.filter(transportation_allowance=transportation_allowance)

    if supervisory_allowance:
        allowances = allowances.filter(supervisory_allowance=supervisory_allowance)

    if technical_allowance:
        allowances = allowances.filter(technical_allowance=technical_allowance)

    # Order by employee name
    allowances = allowances.order_by('employee__full_name')

    # Create search form
    search_form = EmployeeAllowanceSearchForm(request.GET)

    # Calculate statistics
    total_allowances = allowances.count()
    education_count = allowances.filter(education_allowance='yes').count()
    adjustment_count = allowances.filter(adjustment_allowance='yes').count()
    transportation_count = allowances.filter(transportation_allowance='yes').count()
    supervisory_count = allowances.filter(supervisory_allowance='yes').count()
    technical_count = allowances.filter(technical_allowance='yes').count()

    # Handle Excel export
    if request.GET.get('export') == 'excel':
        return export_allowances_to_excel(allowances)

    return render(request, 'employment/employee_allowance_list.html', {
        'allowances': allowances,
        'search_form': search_form,
        'search_term': search_term,
        'education_allowance': education_allowance,
        'adjustment_allowance': adjustment_allowance,
        'transportation_allowance': transportation_allowance,
        'supervisory_allowance': supervisory_allowance,
        'technical_allowance': technical_allowance,
        'total_allowances': total_allowances,
        'education_count': education_count,
        'adjustment_count': adjustment_count,
        'transportation_count': transportation_count,
        'supervisory_count': supervisory_count,
        'technical_count': technical_count,
    })


@login_required
def employee_allowance_create(request):
    """View for creating a new employee allowance"""
    if request.method == 'POST':
        form = EmployeeAllowanceForm(request.POST)
        if form.is_valid():
            allowance = form.save()
            messages.success(request, f'تم إضافة علاوات الموظف "{allowance.employee.full_name}" بنجاح.')
            return redirect('employment:employee_allowance_list')
    else:
        form = EmployeeAllowanceForm()

    return render(request, 'employment/employee_allowance_form.html', {
        'form': form,
        'title': 'إضافة علاوات موظف جديد'
    })


@login_required
def employee_allowance_update(request, pk):
    """View for updating an employee allowance"""
    allowance = get_object_or_404(EmployeeAllowance, pk=pk)

    if request.method == 'POST':
        form = EmployeeAllowanceForm(request.POST, instance=allowance)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث علاوات الموظف "{allowance.employee.full_name}" بنجاح.')
            return redirect('employment:employee_allowance_list')
    else:
        form = EmployeeAllowanceForm(instance=allowance)

    return render(request, 'employment/employee_allowance_form.html', {
        'form': form,
        'allowance': allowance,
        'title': 'تحديث علاوات الموظف'
    })


@login_required
def employee_allowance_delete(request, pk):
    """View for deleting an employee allowance"""
    allowance = get_object_or_404(EmployeeAllowance, pk=pk)

    if request.method == 'POST':
        employee_name = allowance.employee.full_name
        allowance.delete()
        messages.success(request, f'تم حذف علاوات الموظف "{employee_name}" بنجاح.')
        return redirect('employment:employee_allowance_list')

    return render(request, 'employment/employee_allowance_confirm_delete.html', {
        'allowance': allowance
    })


def export_allowances_to_excel(allowances):
    """Export employee allowances to Excel"""
    # Import HttpResponse at the top to avoid UnboundLocalError
    from django.http import HttpResponse

    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from openpyxl.utils import get_column_letter
    except ImportError:
        response = HttpResponse('مكتبة openpyxl غير متوفرة', content_type='text/plain; charset=utf-8')
        return response

    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "علاوات الموظفين"

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    cell_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Headers
    headers = [
        'الرقم',
        'الرقم الوزاري',
        'الاسم الكامل',
        'علاوة التعليم',
        'التجيير',
        'التنقلات',
        'العلاوة الإشرافية',
        'علاوة فنية'
    ]

    # Write headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Write data
    for row, allowance in enumerate(allowances, 2):
        # Get ministry number from employee identification
        ministry_number = allowance.ministry_number

        data = [
            row - 1,  # Serial number
            ministry_number,
            allowance.employee.full_name,
            'نعم' if allowance.education_allowance == 'yes' else 'لا',
            'نعم' if allowance.adjustment_allowance == 'yes' else 'لا',
            'نعم' if allowance.transportation_allowance == 'yes' else 'لا',
            'نعم' if allowance.supervisory_allowance == 'yes' else 'لا',
            'نعم' if allowance.technical_allowance == 'yes' else 'لا',
        ]

        for col, value in enumerate(data, 1):
            cell = ws.cell(row=row, column=col, value=value)
            cell.alignment = cell_alignment
            cell.border = border

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="employee_allowances.xlsx"'

    wb.save(response)
    return response


@login_required
def export_departments_to_excel(request):
    """Export departments to Excel with applied filters"""
    from django.http import HttpResponse

    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from openpyxl.utils import get_column_letter
    except ImportError:
        response = HttpResponse('مكتبة openpyxl غير متوفرة', content_type='text/plain; charset=utf-8')
        return response

    # Get filter parameters from request
    department_filter = request.GET.get('department', '')
    workplace_filter = request.GET.get('workplace', '')
    school_type_filter = request.GET.get('school_type', '')
    school_gender_filter = request.GET.get('school_gender', '')
    directorate_type_filter = request.GET.get('directorate_type', '')

    # Start with all departments
    departments = Department.objects.all()

    # Apply filters
    if department_filter:
        departments = departments.filter(name__icontains=department_filter)

    if workplace_filter:
        departments = departments.filter(workplace=workplace_filter)

    if school_type_filter:
        departments = departments.filter(school_type=school_type_filter)

    if school_gender_filter:
        departments = departments.filter(school_gender=school_gender_filter)

    if directorate_type_filter:
        departments = departments.filter(directorate_type=directorate_type_filter)

    # Order departments
    departments = departments.order_by('name')

    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "الأقسام"

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    cell_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Headers
    headers = [
        'الرقم',
        'القسم',
        'مكان العمل',
        'تصنيف المدرسة',
        'جنس المدرسة',
        'أعلى صف',
        'أدنى صف',
        'يتبع لـ',
        'عدد الموظفين',
        'الوصف'
    ]

    # Write headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Write data
    for row, department in enumerate(departments, 2):
        data = [
            row - 1,  # Serial number
            department.name,
            department.get_workplace_display(),
            department.get_school_type_display() if department.workplace == 'school' else '-',
            department.get_school_gender_display() if department.workplace == 'school' else '-',
            department.get_highest_grade_display() if department.workplace == 'school' and department.highest_grade else '-',
            department.get_lowest_grade_display() if department.workplace == 'school' and department.lowest_grade else '-',
            department.get_directorate_type_display() if department.workplace == 'directorate' else '-',
            department.employees.count(),
            department.description or '-'
        ]

        for col, value in enumerate(data, 1):
            cell = ws.cell(row=row, column=col, value=value)
            cell.alignment = cell_alignment
            cell.border = border

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    # Create filename with filter info
    filename = "departments"
    if department_filter or workplace_filter or school_type_filter or school_gender_filter or directorate_type_filter:
        filename += "_filtered"
    filename += ".xlsx"

    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    wb.save(response)
    return response


# Non-Payment Views
@login_required
def non_payment_list(request):
    """View for listing non-payment records"""
    # Get search parameters
    search_term = request.GET.get('search', '')
    department_filter = request.GET.get('department', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    is_transferred = request.GET.get('is_transferred', '')

    # Start with all non-payment records
    non_payments = NonPayment.objects.select_related('employee').all()

    # Apply filters
    if search_term:
        non_payments = non_payments.filter(
            Q(employee__full_name__icontains=search_term) |
            Q(employee__identifications__ministry_number__icontains=search_term)
        )

    if department_filter:
        non_payments = non_payments.filter(
            employee__employments__department_id=department_filter,
            employee__employments__is_current=True
        )

    if date_from:
        non_payments = non_payments.filter(date__gte=date_from)

    if date_to:
        non_payments = non_payments.filter(date__lte=date_to)

    if is_transferred:
        if is_transferred == 'yes':
            non_payments = non_payments.filter(is_transferred=True)
        elif is_transferred == 'no':
            non_payments = non_payments.filter(is_transferred=False)

    # Get unique departments from current employments of employees with non-payments
    departments_with_non_payments = Department.objects.filter(
        employees__employee__non_payments__isnull=False,
        employees__is_current=True
    ).distinct().order_by('name')

    # Calculate statistics
    total_records = non_payments.count()
    transferred_count = non_payments.filter(is_transferred=True).count()
    pending_count = non_payments.filter(is_transferred=False).count()
    total_days = sum(record.days_count for record in non_payments)

    # Create search form
    search_form = NonPaymentSearchForm(
        request.GET,
        departments_queryset=departments_with_non_payments
    )

    return render(request, 'employment/non_payment_list.html', {
        'non_payments': non_payments,
        'search_form': search_form,
        'search_term': search_term,
        'department_filter': department_filter,
        'date_from': date_from,
        'date_to': date_to,
        'is_transferred': is_transferred,
        'total_records': total_records,
        'transferred_count': transferred_count,
        'pending_count': pending_count,
        'total_days': total_days,
    })


@login_required
def non_payment_create(request):
    """View for adding a new non-payment record"""
    if request.method == 'POST':
        form = NonPaymentForm(request.POST)
        if form.is_valid():
            non_payment = form.save()
            messages.success(request, f'تم إضافة سجل عدم صرف للموظف {non_payment.employee.full_name} بنجاح.')
            return redirect('employment:non_payment_list')
    else:
        form = NonPaymentForm()

    return render(request, 'employment/non_payment_form.html', {
        'form': form,
        'title': 'إضافة سجل عدم صرف'
    })


@login_required
def non_payment_update(request, pk):
    """View for updating a non-payment record"""
    non_payment = get_object_or_404(NonPayment, pk=pk)

    # Check if record is transferred
    if non_payment.is_transferred:
        messages.error(request, 'لا يمكن تعديل سجل تم ترحيله.')
        return redirect('employment:non_payment_list')

    if request.method == 'POST':
        form = NonPaymentForm(request.POST, instance=non_payment)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث سجل عدم صرف للموظف {non_payment.employee.full_name} بنجاح.')
            return redirect('employment:non_payment_list')
    else:
        form = NonPaymentForm(instance=non_payment)

    return render(request, 'employment/non_payment_form.html', {
        'form': form,
        'non_payment': non_payment,
        'title': 'تعديل سجل عدم صرف'
    })


@login_required
def non_payment_delete(request, pk):
    """View for deleting a non-payment record"""
    non_payment = get_object_or_404(NonPayment, pk=pk)

    # Check if record is transferred
    if non_payment.is_transferred:
        messages.error(request, 'لا يمكن حذف سجل تم ترحيله.')
        return redirect('employment:non_payment_list')

    if request.method == 'POST':
        employee_name = non_payment.employee.full_name
        non_payment.delete()
        messages.success(request, f'تم حذف سجل عدم صرف للموظف {employee_name} بنجاح.')
        return redirect('employment:non_payment_list')

    return render(request, 'employment/non_payment_confirm_delete.html', {
        'non_payment': non_payment
    })


@login_required
def non_payment_transfer(request):
    """View for transferring pending non-payment records"""
    if request.method == 'POST':
        # Get all pending (non-transferred) records
        pending_records = NonPayment.objects.filter(is_transferred=False)

        if not pending_records.exists():
            messages.warning(request, 'لا توجد سجلات عدم صرف معلقة للترحيل.')
            return redirect('employment:non_payment_list')

        # Transfer all pending records
        from django.utils import timezone
        transfer_count = pending_records.update(
            is_transferred=True,
            transfer_date=timezone.now()
        )

        messages.success(request, f'تم ترحيل {transfer_count} سجل عدم صرف بنجاح.')
        return redirect('employment:non_payment_summary')

    # Get pending records for confirmation
    pending_records = NonPayment.objects.filter(is_transferred=False).select_related('employee')

    return render(request, 'employment/non_payment_transfer_confirm.html', {
        'pending_records': pending_records
    })


@login_required
def non_payment_summary(request):
    """View for displaying transferred non-payment records summary"""
    # Get only transferred records
    transferred_records = NonPayment.objects.filter(is_transferred=True).select_related('employee').order_by('-transfer_date', 'employee__full_name')

    # Calculate statistics
    total_transferred = transferred_records.count()
    total_days = sum(record.days_count for record in transferred_records)

    # Get latest transfer date
    latest_transfer = transferred_records.first()
    latest_transfer_date = latest_transfer.transfer_date if latest_transfer else None

    return render(request, 'employment/non_payment_summary.html', {
        'transferred_records': transferred_records,
        'total_transferred': total_transferred,
        'total_days': total_days,
        'latest_transfer_date': latest_transfer_date,
    })