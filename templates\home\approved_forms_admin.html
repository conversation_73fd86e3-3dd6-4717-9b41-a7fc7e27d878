{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content_css %}
<style>
    /* أنماط الأيقونات */
    .file-icon {
        font-size: 1.8rem;
        margin-right: 8px;
        transition: transform 0.3s ease;
    }

    .file-row:hover .file-icon {
        transform: scale(1.2);
    }

    .file-pdf {
        color: #dc3545;
    }

    .file-word {
        color: #0d6efd;
    }

    .file-excel {
        color: #198754;
    }

    .file-powerpoint {
        color: #fd7e14;
    }

    .file-archive {
        color: #6c757d;
    }

    .file-text {
        color: #6c757d;
    }

    .file-default {
        color: #6c757d;
    }

    /* أنماط الأزرار */
    .action-buttons .btn {
        margin-right: 5px;
        margin-bottom: 5px;
        border-radius: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #0d6efd, #0a58ca);
        border: none;
    }

    .btn-info {
        background: linear-gradient(135deg, #0dcaf0, #0aa2c0);
        border: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545, #b02a37);
        border: none;
    }

    /* أنماط الجدول */
    .table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    }

    .table thead th {
        background: linear-gradient(135deg, #4e73df, #224abe);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        padding: 15px;
        border: none;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
        transform: scale(1.01);
    }

    .file-row {
        cursor: pointer;
    }

    /* أنماط البطاقات */
    .card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .card-header {
        background: linear-gradient(135deg, #4e73df, #224abe);
        padding: 15px 20px;
        border-bottom: none;
    }

    .card-header h5 {
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .card-header .btn-light {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .card-header .btn-light:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    /* أنماط الشارات */
    .badge {
        padding: 6px 10px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.75rem;
    }

    .badge.bg-success {
        background: linear-gradient(135deg, #1cc88a, #13855c) !important;
    }

    .badge.bg-secondary {
        background: linear-gradient(135deg, #858796, #60616f) !important;
    }

    /* أنماط الترقيم الصفحي */
    .pagination {
        margin-top: 20px;
    }

    .pagination .page-item .page-link {
        border-radius: 50%;
        margin: 0 3px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        color: #4e73df;
        border: none;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .pagination .page-item .page-link:hover {
        background-color: #4e73df;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #4e73df, #224abe);
        color: white;
    }

    /* أنماط التنبيهات */
    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        padding: 15px 20px;
    }

    .alert-info {
        background: linear-gradient(135deg, #36b9cc, #258391);
        color: white;
    }

    .alert-info .alert-link {
        color: white;
        text-decoration: underline;
        font-weight: 600;
    }

    /* تأثيرات الحركة */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .card {
        animation: fadeIn 0.5s ease forwards;
    }

    .table tbody tr {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
    }

    .table tbody tr:nth-child(1) { animation-delay: 0.1s; }
    .table tbody tr:nth-child(2) { animation-delay: 0.2s; }
    .table tbody tr:nth-child(3) { animation-delay: 0.3s; }
    .table tbody tr:nth-child(4) { animation-delay: 0.4s; }
    .table tbody tr:nth-child(5) { animation-delay: 0.5s; }
    .table tbody tr:nth-child(6) { animation-delay: 0.6s; }
    .table tbody tr:nth-child(7) { animation-delay: 0.7s; }
    .table tbody tr:nth-child(8) { animation-delay: 0.8s; }
    .table tbody tr:nth-child(9) { animation-delay: 0.9s; }
    .table tbody tr:nth-child(10) { animation-delay: 1.0s; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-sm-flex align-items-center justify-content-between">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-file-signature fa-sm fa-fw me-2 text-primary"></i>
                    {{ title }}
                </h1>
                <div class="d-flex">
                    <a href="{% url 'home:approved_forms_list' %}" class="btn btn-info me-2">
                        <i class="fas fa-eye me-1"></i> عرض النماذج المعتمدة
                    </a>
                    <a href="{% url 'home:approved_form_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة نموذج جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي النماذج</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ forms.paginator.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                النماذج النشطة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ forms.paginator.count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                أنواع الملفات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                5
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-code fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                آخر تحديث</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ forms.0.created_at|date:"Y-m-d" }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول النماذج -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i> قائمة النماذج المعتمدة
                    </h6>
                    <form class="d-flex" method="get">
                        <div class="input-group">
                            <input class="form-control" type="search" placeholder="بحث..." name="search" value="{{ search_query }}" id="searchInput">
                            <button class="btn btn-outline-primary" type="button" onclick="searchForms()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    {% if forms %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="formsTable">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag me-1"></i> #</th>
                                        <th><i class="fas fa-file-alt me-1"></i> النموذج</th>
                                        <th><i class="fas fa-info-circle me-1"></i> الوصف</th>
                                        <th><i class="fas fa-file-code me-1"></i> نوع الملف</th>
                                        <th><i class="fas fa-weight me-1"></i> الحجم</th>
                                        <th><i class="fas fa-calendar-alt me-1"></i> تاريخ الإضافة</th>
                                        <th><i class="fas fa-sort-numeric-down me-1"></i> الترتيب</th>
                                        <th><i class="fas fa-toggle-on me-1"></i> الحالة</th>
                                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for form in forms %}
                                        <tr class="file-row">
                                            <td>{{ forloop.counter }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="file-icon">
                                                        {% if form.file_type == 'pdf' %}
                                                            <i class="fas fa-file-pdf file-pdf"></i>
                                                        {% elif form.file_type == 'doc' or form.file_type == 'docx' %}
                                                            <i class="fas fa-file-word file-word"></i>
                                                        {% elif form.file_type == 'xls' or form.file_type == 'xlsx' %}
                                                            <i class="fas fa-file-excel file-excel"></i>
                                                        {% elif form.file_type == 'ppt' or form.file_type == 'pptx' %}
                                                            <i class="fas fa-file-powerpoint file-powerpoint"></i>
                                                        {% elif form.file_type == 'zip' or form.file_type == 'rar' %}
                                                            <i class="fas fa-file-archive file-archive"></i>
                                                        {% elif form.file_type == 'txt' %}
                                                            <i class="fas fa-file-alt file-text"></i>
                                                        {% else %}
                                                            <i class="fas fa-file file-default"></i>
                                                        {% endif %}
                                                    </span>
                                                    <strong>{{ form.title }}</strong>
                                                </div>
                                            </td>
                                            <td>{{ form.description|default:"-"|truncatechars:50 }}</td>
                                            <td><span class="badge bg-light text-dark">{{ form.file_type|upper }}</span></td>
                                            <td>{{ form.file.size|filesizeformat }}</td>
                                            <td>{{ form.created_at|date:"Y-m-d" }}</td>
                                            <td>{{ form.order }}</td>
                                            <td>
                                                {% if form.is_active %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'home:approved_form_download' form.id %}" class="btn btn-info btn-sm" title="تحميل">
                                                    <i class="fas fa-download"></i> تحميل
                                                </a>
                                                <a href="{% url 'home:approved_form_update' form.id %}" class="btn btn-warning btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </a>
                                                <a href="{% url 'home:approved_form_delete' form.id %}" class="btn btn-danger btn-sm" title="حذف">
                                                    <i class="fas fa-trash"></i> حذف
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if forms.has_other_pages %}
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if forms.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ forms.previous_page_number }}" aria-label="Previous">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-chevron-right"></i>
                                            </span>
                                        </li>
                                    {% endif %}

                                    {% for i in forms.paginator.page_range %}
                                        {% if forms.number == i %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ i }}</span>
                                            </li>
                                        {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if forms.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ forms.next_page_number }}" aria-label="Next">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-chevron-left"></i>
                                            </span>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info text-center p-4">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <h5>لا توجد نماذج متاحة حالياً</h5>
                            <p class="mb-3">لم يتم إضافة أي نماذج معتمدة بعد. يمكنك إضافة نماذج جديدة من خلال النقر على الزر أدناه.</p>
                            <a href="{% url 'home:approved_form_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-1"></i> إضافة نموذج جديد
                            </a>
                        </div>
                    {% endif %}

                    <!-- إضافة سكريبت البحث -->
                    <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const searchInput = document.getElementById('searchInput');
                        const formsTable = document.getElementById('formsTable');
                        const rows = formsTable.getElementsByTagName('tr');

                        // إضافة حدث للبحث عند الضغط على Enter
                        searchInput.addEventListener('keyup', function(event) {
                            if (event.key === 'Enter') {
                                searchForms();
                            }
                        });
                    });

                    // دالة البحث
                    function searchForms() {
                        const searchInput = document.getElementById('searchInput');
                        const formsTable = document.getElementById('formsTable');
                        const rows = formsTable.querySelectorAll('tbody tr');
                        const searchText = searchInput.value.toLowerCase();

                        rows.forEach(row => {
                            const rowText = row.textContent.toLowerCase();
                            if (rowText.includes(searchText)) {
                                row.style.display = '';
                            } else {
                                row.style.display = 'none';
                            }
                        });
                    }
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
