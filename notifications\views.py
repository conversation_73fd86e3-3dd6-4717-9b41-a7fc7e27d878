from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from .models import Notification

@login_required
def notification_list(request):
    """View to display all notifications for the current user"""
    notifications = Notification.get_user_notifications(request.user, limit=50)
    return render(request, 'notifications/notification_list.html', {
        'notifications': notifications
    })

@login_required
@require_POST
def mark_notification_read(request, notification_id):
    """Mark a specific notification as read"""
    try:
        notification = get_object_or_404(
            Notification, 
            id=notification_id,
            user=request.user
        )
        notification.mark_as_read()
        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_POST
def mark_all_notifications_read(request):
    """Mark all notifications as read for the current user"""
    try:
        notifications = Notification.objects.filter(
            user=request.user,
            is_read=False
        )
        for notification in notifications:
            notification.mark_as_read()
        
        return JsonResponse({
            'success': True, 
            'message': 'تم تحديد جميع الإشعارات كمقروءة'
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_notifications_ajax(request):
    """Get notifications via AJAX"""
    notifications = Notification.get_user_notifications(request.user, limit=10)
    unread_count = Notification.get_unread_count(request.user)
    
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message,
            'type': notification.notification_type,
            'icon': notification.icon,
            'is_read': notification.is_read,
            'time_since': notification.get_time_since_created(),
            'background_color': notification.get_background_color(),
        })
    
    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': unread_count
    })
