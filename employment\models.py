from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from employees.models import Employee

class Department(models.Model):
    """Model for storing departments"""
    WORKPLACE_CHOICES = [
        ('directorate', _('المديرية')),
        ('school', _('المدارس')),
    ]

    SCHOOL_TYPE_CHOICES = [
        ('primary', _('اساسي')),
        ('secondary', _('ثانوي')),
    ]

    SCHOOL_GENDER_CHOICES = [
        ('male', _('ذكور')),
        ('female', _('اناث')),
        ('mixed', _('مختلطة')),
    ]

    DIRECTORATE_TYPE_CHOICES = [
        ('manager', _('يتبع للمدير')),
        ('administrative', _('الأقسام الإدارية')),
        ('educational', _('الأقسام التعليمية')),
    ]

    GRADE_CHOICES = [
        ('kg', _('رياض الأطفال')),
        ('grade1', _('الصف الأول')),
        ('grade2', _('الصف الثاني')),
        ('grade3', _('الصف الثالث')),
        ('grade4', _('الصف الرابع')),
        ('grade5', _('الصف الخامس')),
        ('grade6', _('الصف السادس')),
        ('grade7', _('الصف السابع')),
        ('grade8', _('الصف الثامن')),
        ('grade9', _('الصف التاسع')),
        ('grade10', _('الصف العاشر')),
        ('grade11', _('الصف الحادي عشر')),
        ('grade12', _('الصف الثاني عشر')),
    ]

    name = models.CharField(_('Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    workplace = models.CharField(_('Workplace'), max_length=20, choices=WORKPLACE_CHOICES, default='directorate')
    school_type = models.CharField(_('School Type'), max_length=20, choices=SCHOOL_TYPE_CHOICES, blank=True, null=True)
    school_gender = models.CharField(_('School Gender'), max_length=20, choices=SCHOOL_GENDER_CHOICES, blank=True, null=True)
    highest_grade = models.CharField(_('Highest Grade'), max_length=20, choices=GRADE_CHOICES, blank=True, null=True)
    lowest_grade = models.CharField(_('Lowest Grade'), max_length=20, choices=GRADE_CHOICES, blank=True, null=True)
    directorate_type = models.CharField(_('Directorate Type'), max_length=20, choices=DIRECTORATE_TYPE_CHOICES, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['name']

    def __str__(self):
        return self.name

class Position(models.Model):
    """Model for storing positions"""
    name = models.CharField(_('Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Position')
        verbose_name_plural = _('Positions')
        ordering = ['name']

    def __str__(self):
        return self.name

class EmploymentStatus(models.Model):
    """Model for storing employment status types (permanent, temporary, contract)"""
    PERMANENT = 'permanent'
    TEMPORARY = 'temporary'
    CONTRACT = 'contract'

    STATUS_CHOICES = [
        (PERMANENT, _('Permanent')),
        (TEMPORARY, _('Temporary')),
        (CONTRACT, _('Contract')),
    ]

    name = models.CharField(_('Name'), max_length=20, choices=STATUS_CHOICES, unique=True)
    description = models.TextField(_('Description'), blank=True, null=True)

    class Meta:
        verbose_name = _('Employment Status')
        verbose_name_plural = _('Employment Statuses')

    def __str__(self):
        return self.get_name_display()


class AppointmentType(models.Model):
    """Model for storing appointment types (صفة التعيين)"""
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Appointment Type')
        verbose_name_plural = _('Appointment Types')
        ordering = ['name']

    def __str__(self):
        return self.name

class Employment(models.Model):
    """Model for storing employment details"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='employments')
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='employees')
    position = models.ForeignKey(Position, on_delete=models.CASCADE, related_name='employees')
    status = models.ForeignKey(EmploymentStatus, on_delete=models.CASCADE, related_name='employees')
    appointment_type = models.ForeignKey(AppointmentType, on_delete=models.SET_NULL, related_name='employees', null=True, blank=True)
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'), blank=True, null=True)
    is_current = models.BooleanField(_('Is Current'), default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employment')
        verbose_name_plural = _('Employments')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.department.name} - {self.position.name}"


class TechnicalPosition(models.Model):
    """Model for storing technical positions (الموقف الفني)"""
    GENDER_CHOICES = [
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    ]

    specialization = models.CharField(_('التخصص'), max_length=255)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='technical_positions', verbose_name=_('القسم'), null=True, blank=True)
    gender = models.CharField(_('الجنس'), max_length=10, choices=GENDER_CHOICES)
    vacancies = models.PositiveIntegerField(_('عدد الشواغر'))
    notes = models.TextField(_('ملاحظات (المبرر)'), blank=False, null=False, help_text=_('يرجى إدخال المبرر من الشاغر'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('الموقف الفني')
        verbose_name_plural = _('المواقف الفنية')
        ordering = ['specialization']

    def __str__(self):
        return f"{self.specialization} - {self.get_gender_display()} - {self.vacancies}"


class EmployeePosition(models.Model):
    """Model for storing employee position history (الحراك الوظيفي)"""
    SCHOOL_LEVEL_CHOICES = [
        ('primary', _('اساسي')),
        ('secondary', _('ثانوي')),
        ('both', _('اساسي + ثانوي')),
    ]

    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='positions', verbose_name=_('الموظف'))
    position = models.ForeignKey(Position, on_delete=models.CASCADE, related_name='employee_positions', verbose_name=_('المسمى الوظيفي'))
    date_obtained = models.DateField(_('تاريخ الحصول عليه'))
    school_level = models.CharField(_('المرحلة'), max_length=20, choices=SCHOOL_LEVEL_CHOICES, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Print debug information
        print(f"Saving EmployeePosition: employee={self.employee}, position={self.position}, date={self.date_obtained}")
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _('المسمى الوظيفي للموظف')
        verbose_name_plural = _('المسميات الوظيفية للموظفين')
        ordering = ['-date_obtained']

    def __str__(self):
        return f"{self.employee.full_name} - {self.position.name} - {self.date_obtained}"


class EmployeeIdentification(models.Model):
    """Model for storing employee identification data (البيانات التعريفية للموظف)"""
    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='identifications', verbose_name=_('الموظف'))
    ministry_number = models.CharField(_('الرقم الوزاري'), max_length=20)
    national_id = models.CharField(_('الرقم الوطني'), max_length=20)
    id_number = models.CharField(_('رقم الهوية'), max_length=20)
    birth_day = models.IntegerField(_('يوم الميلاد'), choices=[(i, i) for i in range(1, 32)])
    birth_month = models.IntegerField(_('شهر الميلاد'), choices=[(i, i) for i in range(1, 13)])
    birth_year = models.IntegerField(_('سنة الميلاد'))
    address = models.TextField(_('العنوان'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('بيانات تعريفية للموظف')
        verbose_name_plural = _('البيانات التعريفية للموظفين')
        ordering = ['employee__full_name']

    def __str__(self):
        return f"{self.employee.full_name} - {self.ministry_number}"


class ExcessEmployee(models.Model):
    """Model for storing excess employees (الموظفين الزوائد)"""
    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='excess_records', verbose_name=_('الموظف'))
    current_department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='excess_employees', verbose_name=_('القسم الحالي'), null=True, blank=True)
    position = models.ForeignKey(Position, on_delete=models.CASCADE, related_name='excess_employees', verbose_name=_('المسمى الوظيفي'), null=True, blank=True)
    reason = models.TextField(_('سبب الزيادة'), help_text=_('سبب اعتبار الموظف زائد في القسم'))
    status = models.CharField(_('الحالة'), max_length=20, choices=[
        ('pending', _('قيد المعالجة')),
        ('resolved', _('تمت المعالجة')),
    ], default='pending')
    resolution_notes = models.TextField(_('ملاحظات المعالجة'), blank=True, null=True)
    resolution_date = models.DateField(_('تاريخ المعالجة'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('موظف زائد')
        verbose_name_plural = _('الموظفين الزوائد')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.current_department.name}"

    def resolve(self, notes):
        """Mark the excess employee record as resolved"""
        self.status = 'resolved'
        self.resolution_notes = notes
        self.resolution_date = timezone.now().date()
        self.save()

        # Return the updated count of pending excess employees
        return ExcessEmployee.objects.filter(status='pending').count()


class MedicalConditionName(models.Model):
    """Model for storing medical condition names (أسماء الحالات المرضية)"""
    name = models.CharField(_('اسم الحالة المرضية'), max_length=255, unique=True)
    description = models.TextField(_('وصف الحالة'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('اسم حالة مرضية')
        verbose_name_plural = _('أسماء الحالات المرضية')
        ordering = ['name']

    def __str__(self):
        return self.name


class MedicalCondition(models.Model):
    """Model for storing employee medical conditions (الحالات المرضية)"""
    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='medical_conditions', verbose_name=_('الموظف'))
    condition = models.ForeignKey(MedicalConditionName, on_delete=models.PROTECT, related_name='medical_conditions', verbose_name=_('الحالة المرضية'))
    medical_report_date = models.DateField(_('تاريخ التقرير الطبي'))
    description = models.TextField(_('وصف الحالة'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('حالة مرضية')
        verbose_name_plural = _('الحالات المرضية')
        ordering = ['-medical_report_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.condition.name}"


class BtecField(models.Model):
    """Model for storing BTEC fields (حقول BTEC)"""
    name = models.CharField(_('اسم الحقل'), max_length=255, unique=True)
    description = models.TextField(_('وصف الحقل'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('حقل BTEC')
        verbose_name_plural = _('حقول BTEC')
        ordering = ['name']

    def __str__(self):
        return self.name


class BtecJob(models.Model):
    """Model for storing BTEC jobs/positions (وظائف BTEC)"""
    name = models.CharField(_('اسم الوظيفة'), max_length=255, unique=True)
    description = models.TextField(_('وصف الوظيفة'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('وظيفة BTEC')
        verbose_name_plural = _('وظائف BTEC')
        ordering = ['name']

    def __str__(self):
        return self.name


class BtecTeacher(models.Model):
    """Model for storing BTEC teachers (معلمي BTEC)"""
    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='btec_records', verbose_name=_('الموظف'))
    field = models.ForeignKey(BtecField, on_delete=models.CASCADE, related_name='teachers', verbose_name=_('الحقل'))
    job = models.ForeignKey(BtecJob, on_delete=models.CASCADE, related_name='teachers', verbose_name=_('الوظيفة'), default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('معلم BTEC')
        verbose_name_plural = _('معلمي BTEC')
        ordering = ['employee__full_name']
        unique_together = ['employee', 'field', 'job']  # منع تكرار نفس الموظف في نفس الحقل والوظيفة

    def __str__(self):
        return f"{self.employee.full_name} - {self.field.name} - {self.job.name}"

    @property
    def ministry_number(self):
        """Get ministry number from employee identification"""
        identification = self.employee.identifications.first()
        return identification.ministry_number if identification else '-'

    @property
    def department_name(self):
        """Get current department name"""
        current_employment = self.employee.employments.filter(is_current=True).first()
        return current_employment.department.name if current_employment else '-'

    @property
    def specialization(self):
        """Get employee specialization"""
        return self.employee.specialization if hasattr(self.employee, 'specialization') and self.employee.specialization else '-'


class EmployeeAllowance(models.Model):
    """Model for storing employee allowances (علاوات الموظفين)"""

    ALLOWANCE_CHOICES = [
        ('yes', 'نعم'),
        ('no', 'لا'),
    ]

    employee = models.OneToOneField(
        'employees.Employee',
        on_delete=models.CASCADE,
        related_name='allowances',
        verbose_name=_('الموظف')
    )

    # Education allowance (علاوة التعليم)
    education_allowance = models.CharField(
        _('علاوة التعليم'),
        max_length=3,
        choices=ALLOWANCE_CHOICES,
        default='no'
    )

    # Adjustment allowance (التجيير)
    adjustment_allowance = models.CharField(
        _('التجيير'),
        max_length=3,
        choices=ALLOWANCE_CHOICES,
        default='no'
    )

    # Transportation allowance (التنقلات)
    transportation_allowance = models.CharField(
        _('التنقلات'),
        max_length=3,
        choices=ALLOWANCE_CHOICES,
        default='no'
    )

    # Supervisory allowance (العلاوة الإشرافية)
    supervisory_allowance = models.CharField(
        _('العلاوة الإشرافية'),
        max_length=3,
        choices=ALLOWANCE_CHOICES,
        default='no'
    )

    # Technical allowance (علاوة فنية)
    technical_allowance = models.CharField(
        _('علاوة فنية'),
        max_length=3,
        choices=ALLOWANCE_CHOICES,
        default='no'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('علاوات الموظف')
        verbose_name_plural = _('علاوات الموظفين')
        ordering = ['employee__full_name']

    def __str__(self):
        return f"علاوات {self.employee.full_name}"

    @property
    def ministry_number(self):
        """Get ministry number from employee identification"""
        identification = self.employee.identifications.first()
        return identification.ministry_number if identification else '-'
