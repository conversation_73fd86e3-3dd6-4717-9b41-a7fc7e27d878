{% load static %}
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كتاب النقل الفني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    body {
        font-family: Arial, sans-serif;
        padding: 20px;
    }

    @page {
        margin: 1cm 1.5cm 0.5cm 1.5cm; /* Top, Right, Bottom, Left */
        size: A4 portrait;
    }

    .letter-container {
        max-width: 21cm;
        margin: 0 auto;
        position: relative;
    }

    .letter-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .letter-header h1 {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .ministry-logo {
        width: 80px;
        height: 80px;
        margin: 10px auto;
        display: block;
    }

    .letter-body {
        line-height: 1.8;
        text-align: right;
    }

    .recipient {
        margin-bottom: 30px;
        text-align: center;
    }

    .recipient-title {
        font-size: 16px;
        font-weight: bold;
    }

    .subject {
        margin-bottom: 20px;
        text-align: left;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .subject-line {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 5px;
    }

    .subject-label {
        min-width: 80px;
        margin-left: 5px;
    }

    .subject-text {
        margin-right: 5px;
    }

    .greeting {
        margin-bottom: 20px;
    }

    .main-content {
        margin-bottom: 10px;
        text-align: justify;
    }

    .closing {
        margin-top: 10px;
        margin-bottom: 20px;
    }

    .letter-footer {
        margin-top: 30px;
        text-align: left;
    }

    .signature-line {
        display: block;
        width: 100%;
        margin-top: 20px;
        text-align: center;
    }

    .signature-title {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        margin-top: 0;
    }

    .copies {
        margin-top: 20px;
        text-align: right;
    }

    .copies-list {
        margin-right: 20px;
        line-height: 1.5;
    }

    .form-number {
        position: absolute;
        bottom: -30px;
        right: 0;
        font-size: 12px;
    }

    .page-divider {
        border-top: 1px dashed #ccc;
        margin: 30px 0;
    }

    .button-container {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin: 40px 0 20px;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        text-decoration: none;
        display: inline-block;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            padding: 0;
            margin: 0;
        }

        @page {
            margin: 1cm 1.5cm 0.5cm 1.5cm; /* Top, Right, Bottom, Left */
            size: A4 portrait;
        }

        .letter-container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
    }
</style>
</head>
<body>
<div class="container">
    <div class="letter-container">
        <div class="letter-header">

            <h1>المملكة الأردنية الهاشمية</h1>
             <img src="{% static 'img/logos/moe-logo.jpg' %}" alt="شعار وزارة التربية والتعليم" class="ministry-logo">
            <h1>وزارة التربية والتعليم</h1>
            <h1>مديرية التربية والتعليم / لواء قصبة المفرق</h1>

        </div>

        <div class="letter-body">
            <div class="recipient">
                <p class="recipient-title">مدير/ة {{ transfer.current_department }}</p>
            </div>

            <div style="display: flex; justify-content: flex-end; margin-bottom: 20px;">
                <div class="subject">
                    <div class="subject-line">
                        <span class="subject-label">الموضوع / </span>
                        <span class="subject-text">النقل الداخلي الفني</span>
                    </div>
                    <div class="subject-line">
                        <span class="subject-label">و{{ job_title }} / </span>
                        <span class="subject-text">{{ transfer.employee_name }}</span>
                    </div>
                </div>
            </div>

           <div class="greeting">
            <p>السلام عليكم ورحمة الله وبركاته ...</p>
        </div>

            <div class="main-content">
                <p style="margin-bottom: 0;">
                    استنادا لأحكام المادة (١/٤٣) من نظام الموارد البشرية في القطاع العام رقم (٣٣) لسنة ٢٠٢٤
                    وبموجب الصلاحيات المفوضة لي بكتاب معالي وزير التربية والتعليم رقم : 13668/70/1 تاريخ :
                    2050/3/16، وبناءً على تنسيب مدير الشؤون الإدارية والمالية قررت نقل {{ job_title }} المذكور اعلاه
                    الى {{ new_department }} ( فنيا ) اعتبارا من تاريخه عليه الحصول على الانفكاك وبراءة
                    الذمة حسب الاصول.
                </p>
            </div>

            <div class="closing">
                <p style="margin-bottom: 0; text-align: center; font-weight: bold;">وتفضلوا بقبول فائق الاحترام</p>
                <div style="margin-top: 0;">
                    <p style="margin-top: 5px; margin-bottom: 0;">نسخة:</p>
                    <div style="margin-top: 0;">
                        <p style="margin: 0;">مدير الشؤون الإدارية والمالية</p>
                        <p style="margin: 0;">ر.ق شؤون الموظفين</p>
                        <p style="margin: 0;">الملف</p>
                        <p style="margin: 0;">مدير/ة {{ new_department }} لاعلامي بتاريخ مباشرة العمل</p>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>

<div class="button-container no-print">
    <button class="btn btn-primary" id="print-button">
        <i class="fas fa-print"></i> طباعة
    </button>
    <a href="{% url 'home:technical_transfer_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>
</div>

<script>
    // Print function
    document.getElementById('print-button').addEventListener('click', function() {
        window.print();
    });
</script>
</body>
</html>
