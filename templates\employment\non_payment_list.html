{% extends 'base.html' %}
{% load static %}

{% block title %}عدم الصرف - الإجراءات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border-width: 2px !important;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .stats-card .card-body {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    }

    .stats-card .h3 {
        font-size: 2.2rem;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .stats-card .card-title {
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-card small {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    .stats-card i {
        font-size: 1.1rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .stats-card .h3 {
            font-size: 1.8rem;
        }
        .stats-card .card-title {
            font-size: 0.75rem;
        }
    }

    .transferred-row {
        background-color: #f8f9fa !important;
        opacity: 0.8;
    }

    .transferred-badge {
        background-color: #6c757d !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-ban text-danger me-2"></i>
                عدم الصرف
            </h1>
            <p class="text-muted mb-0">إدارة سجلات عدم الصرف للموظفين</p>
        </div>
        <div>
            <a href="{% url 'employment:non_payment_create' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus"></i> إضافة سجل عدم صرف
            </a>
            {% if pending_count > 0 %}
            <a href="{% url 'employment:non_payment_transfer' %}" class="btn btn-warning me-2">
                <i class="fas fa-exchange-alt"></i> ترحيل السجلات ({{ pending_count }})
            </a>
            {% endif %}
            {% if transferred_count > 0 %}
            <a href="{% url 'employment:non_payment_cancel_transfer' %}" class="btn btn-danger me-2">
                <i class="fas fa-undo"></i> إلغاء آخر ترحيل
            </a>
            {% endif %}
            <a href="{% url 'employment:non_payment_summary' %}" class="btn btn-info me-2">
                <i class="fas fa-file-alt"></i> ملخص عدم الصرف
            </a>
            <button type="button" id="printBtn" class="btn btn-success">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <!-- Alert for success messages -->
    <div id="alert-container"></div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card border-primary shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        <h6 class="card-title mb-0 text-primary">إجمالي السجلات</h6>
                    </div>
                    <div class="h3 mb-1 text-primary font-weight-bold">{{ total_records }}</div>
                    <small class="text-muted">سجل</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-warning shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-clock text-warning me-2"></i>
                        <h6 class="card-title mb-0 text-warning">معلق</h6>
                    </div>
                    <div class="h3 mb-1 text-warning font-weight-bold">{{ pending_count }}</div>
                    <small class="text-muted">من {{ total_records }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-success shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <h6 class="card-title mb-0 text-success">تم الترحيل</h6>
                    </div>
                    <div class="h3 mb-1 text-success font-weight-bold">{{ transferred_count }}</div>
                    <small class="text-muted">من {{ total_records }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-info shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-calendar-times text-info me-2"></i>
                        <h6 class="card-title mb-0 text-info">إجمالي الأيام</h6>
                    </div>
                    <div class="h3 mb-1 text-info font-weight-bold">{{ total_days }}</div>
                    <small class="text-muted">يوم</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث
            </h5>
        </div>
        <div class="card-body py-3">
            <form method="get" class="row g-3">
                <!-- Department Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-building text-primary"></i> القسم
                    </label>
                    {{ search_form.department }}
                </div>

                <!-- Date From Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-calendar text-success"></i> من تاريخ
                    </label>
                    {{ search_form.date_from }}
                </div>

                <!-- Date To Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-calendar text-info"></i> إلى تاريخ
                    </label>
                    {{ search_form.date_to }}
                </div>

                <!-- Transfer Status Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-exchange-alt text-warning"></i> حالة الترحيل
                    </label>
                    {{ search_form.is_transferred }}
                </div>

                <!-- Submit Button -->
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>

                <!-- Reset Button -->
                <div class="col-md-2 d-flex align-items-end">
                    <a href="{% url 'employment:non_payment_list' %}" class="btn btn-outline-secondary btn-sm w-100">
                        <i class="fas fa-undo"></i> إعادة ضبط
                    </a>
                </div>
            </form>

            <!-- Filter Results Info -->
            <div class="row mt-2">
                <div class="col-12">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        عرض {{ total_records }} من سجلات عدم الصرف
                        {% if search_term or department_filter or date_from or date_to or is_transferred %}
                            (مفلترة)
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control" placeholder="البحث بالرقم الوزاري أو الاسم..." value="{{ search_term }}">
                <button class="btn btn-outline-secondary btn-sm" type="button" id="resetSearch">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <span class="badge bg-info fs-6" id="recordCount">{{ total_records }} سجل</span>
        </div>
    </div>

    <!-- Table Section -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="nonPaymentTable">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 8%;">
                                <i class="fas fa-hashtag me-2"></i>الرقم
                            </th>
                            <th style="width: 15%;">
                                <i class="fas fa-id-card me-2"></i>الرقم الوزاري
                            </th>
                            <th style="width: 20%;">
                                <i class="fas fa-user me-2"></i>الاسم الكامل
                            </th>
                            <th style="width: 15%;">
                                <i class="fas fa-building me-2"></i>القسم
                            </th>
                            <th style="width: 12%;">
                                <i class="fas fa-calendar me-2"></i>التاريخ
                            </th>
                            <th style="width: 10%;">
                                <i class="fas fa-calendar-times me-2"></i>عدد الأيام
                            </th>
                            <th style="width: 15%;">
                                <i class="fas fa-sticky-note me-2"></i>الملاحظات
                            </th>
                            <th style="width: 15%;">
                                <i class="fas fa-cogs me-2"></i>الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for non_payment in non_payments %}
                        <tr {% if non_payment.is_transferred %}class="transferred-row"{% endif %}>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <strong>{{ non_payment.ministry_number }}</strong>
                            </td>
                            <td>
                                <strong>{{ non_payment.employee.full_name }}</strong>
                            </td>
                            <td>{{ non_payment.department_name }}</td>
                            <td>{{ non_payment.date|date:"Y-m-d" }}</td>
                            <td>
                                <span class="badge bg-warning">{{ non_payment.days_count }} يوم</span>
                            </td>
                            <td>
                                {% if non_payment.notes %}
                                    <span title="{{ non_payment.notes }}">{{ non_payment.notes|truncatechars:30 }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if not non_payment.is_transferred %}
                                    <a href="{% url 'employment:non_payment_update' non_payment.pk %}"
                                       class="btn btn-sm btn-warning me-1" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'employment:non_payment_delete' non_payment.pk %}"
                                       class="btn btn-sm btn-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                {% else %}
                                    <span class="badge transferred-badge">تم الترحيل</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <h5>لا توجد سجلات عدم صرف</h5>
                                    <p>لم يتم العثور على أي سجلات عدم صرف.</p>
                                    <a href="{% url 'employment:non_payment_create' %}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة سجل عدم صرف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize Select2 for department dropdown
    $('#id_department').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر القسم...',
        allowClear: true
    });

    // Get total count for calculations
    var totalCount = $('#nonPaymentTable tbody tr').length;
    var visibleCount = totalCount;

    // Search functionality (instant search like allowances page)
    $('#searchInput').on('keyup', function() {
        applySearch();
    });

    // Reset search
    $('#resetSearch').on('click', function() {
        $('#searchInput').val('');
        applySearch();
    });

    function applySearch() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var visibleCount = 0;

        $('#nonPaymentTable tbody tr').each(function() {
            var $row = $(this);
            var show = true;

            // Skip empty rows
            if ($row.find('td').length <= 1 || $row.find('td[colspan]').length > 0) {
                return;
            }

            // Search filter (ministry number and name)
            if (searchValue) {
                var ministryNumber = $row.find('td:eq(1)').text().toLowerCase();
                var employeeName = $row.find('td:eq(2)').text().toLowerCase();

                if (ministryNumber.indexOf(searchValue) === -1 &&
                    employeeName.indexOf(searchValue) === -1) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update record count
        $('#recordCount').text(visibleCount + ' سجل');
    }

    // Initial search application
    applySearch();

    // Print functionality
    $('#printBtn').on('click', function() {
        printNonPaymentReport();
    });

    function printNonPaymentReport() {
        // Create a new window for printing
        const printWindow = window.open('', '_blank');

        // Get current filters
        const searchTerm = $('#searchInput').val() || '';
        const department = $('#id_department option:selected').text() || 'الكل';
        const dateFrom = $('input[name="date_from"]').val() || '';
        const dateTo = $('input[name="date_to"]').val() || '';
        const transferStatus = $('#id_is_transferred option:selected').text() || 'الكل';

        // Build filter info (include instant search)
        let filterInfo = '';
        const instantSearchTerm = $('#searchInput').val();
        if (instantSearchTerm) filterInfo += `البحث الفوري: ${instantSearchTerm} | `;
        if (searchTerm && searchTerm !== instantSearchTerm) filterInfo += `البحث: ${searchTerm} | `;
        if (department !== 'الكل') filterInfo += `القسم: ${department} | `;
        if (dateFrom) filterInfo += `من تاريخ: ${dateFrom} | `;
        if (dateTo) filterInfo += `إلى تاريخ: ${dateTo} | `;
        if (transferStatus !== 'الكل') filterInfo += `حالة الترحيل: ${transferStatus}`;

        // Remove trailing separator
        filterInfo = filterInfo.replace(/ \| $/, '');

        // Get table data (only visible rows)
        const tableRows = [];
        $('#nonPaymentTable tbody tr:visible').each(function() {
            if (!$(this).find('td[colspan]').length) { // Skip empty state row
                const row = [];
                $(this).find('td').each(function(index) {
                    if (index < 7) { // Exclude actions column
                        let cellText = $(this).text().trim();
                        // Clean up badge text
                        if ($(this).find('.badge').length) {
                            cellText = $(this).find('.badge').text().trim();
                        }
                        row.push(cellText);
                    }
                });
                tableRows.push(row);
            }
        });

        // Create print content
        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير عدم الصرف</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        text-align: right;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .header h1 {
                        color: #333;
                        margin-bottom: 10px;
                    }
                    .header p {
                        color: #666;
                        margin: 5px 0;
                    }
                    .filters {
                        background-color: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        margin-bottom: 20px;
                    }
                    .filters h3 {
                        margin-top: 0;
                        color: #495057;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    th {
                        background-color: #343a40;
                        color: white;
                        font-weight: bold;
                    }
                    tr:nth-child(even) {
                        background-color: #f2f2f2;
                    }
                    .footer {
                        margin-top: 30px;
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }
                    .stats {
                        display: flex;
                        justify-content: space-around;
                        margin-bottom: 20px;
                        background-color: #e9ecef;
                        padding: 15px;
                        border-radius: 5px;
                    }
                    .stat-item {
                        text-align: center;
                    }
                    .stat-number {
                        font-size: 24px;
                        font-weight: bold;
                        color: #495057;
                    }
                    .stat-label {
                        font-size: 12px;
                        color: #6c757d;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير عدم الصرف</h1>
                    <p>مديرية قصبة المفرق - قسم شؤون الموظفين</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('en-GB')}</p>
                </div>

                ${filterInfo ? `
                <div class="filters">
                    <h3>فلاتر التقرير:</h3>
                    <p>${filterInfo}</p>
                </div>
                ` : ''}

                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">${tableRows.length}</div>
                        <div class="stat-label">السجلات المعروضة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ pending_count }}</div>
                        <div class="stat-label">معلق (إجمالي)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ transferred_count }}</div>
                        <div class="stat-label">تم الترحيل (إجمالي)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ total_days }}</div>
                        <div class="stat-label">إجمالي الأيام</div>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الرقم الوزاري</th>
                            <th>الاسم الكامل</th>
                            <th>القسم</th>
                            <th>التاريخ</th>
                            <th>عدد الأيام</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows.map(row => `
                            <tr>
                                ${row.map(cell => `<td>${cell}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="footer">
                    <p>مديرية قصبة المفرق - قسم شؤون الموظفين</p>
                </div>
            </body>
            </html>
        `;

        // Write content to print window
        printWindow.document.write(printContent);
        printWindow.document.close();

        // Wait for content to load then print
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    }
});
</script>
{% endblock %}
