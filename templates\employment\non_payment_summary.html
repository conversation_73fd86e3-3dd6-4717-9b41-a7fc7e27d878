{% extends 'base.html' %}
{% load static %}

{% block title %}ملخص عدم الصرف - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .summary-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .summary-card .card-header {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border: none;
        padding: 20px;
    }

    .stats-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border-width: 2px !important;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .stats-card .card-body {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    }

    .stats-card .h3 {
        font-size: 2.2rem;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .transferred-row {
        background-color: #e8f5e8 !important;
    }

    .print-header {
        display: none;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .print-header {
            display: block !important;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background: #f8f9fa !important;
            color: #333 !important;
        }

        body {
            font-size: 12px;
        }

        .table {
            font-size: 11px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Print Header (hidden on screen) -->
    <div class="print-header">
        <h1>ملخص عدم الصرف</h1>
        <p>مديرية قصبة المفرق - قسم شؤون الموظفين</p>
        <p>تاريخ الطباعة: <span id="printDate"></span></p>
        {% if latest_transfer_date %}
        <p>آخر ترحيل: {{ latest_transfer_date|date:"Y-m-d H:i" }}</p>
        {% endif %}
    </div>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-file-alt text-info me-2"></i>
                ملخص عدم الصرف
            </h1>
            <p class="text-muted mb-0">عرض السجلات المرحلة لعدم الصرف</p>
        </div>
        <div>
            <a href="{% url 'employment:non_payment_list' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> العودة إلى القائمة
            </a>
            <a href="{% url 'employment:non_payment_summary' %}" class="btn btn-success" id="print-summary-btn">
                <i class="fas fa-print"></i> طباعة الملخص
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card stats-card border-info shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-check-circle text-info me-2"></i>
                        <h6 class="card-title mb-0 text-info">السجلات المرحلة</h6>
                    </div>
                    <div class="h3 mb-1 text-info font-weight-bold">{{ total_transferred }}</div>
                    <small class="text-muted">سجل</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card stats-card border-warning shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-calendar-times text-warning me-2"></i>
                        <h6 class="card-title mb-0 text-warning">إجمالي الأيام</h6>
                    </div>
                    <div class="h3 mb-1 text-warning font-weight-bold">{{ total_days }}</div>
                    <small class="text-muted">يوم</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card stats-card border-success shadow-sm">
                <div class="card-body text-center py-3">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-clock text-success me-2"></i>
                        <h6 class="card-title mb-0 text-success">آخر ترحيل</h6>
                    </div>
                    {% if latest_transfer_date %}
                    <div class="h6 mb-1 text-success font-weight-bold">{{ latest_transfer_date|date:"Y-m-d" }}</div>
                    <small class="text-muted">{{ latest_transfer_date|date:"H:i" }}</small>
                    {% else %}
                    <div class="h6 mb-1 text-muted">-</div>
                    <small class="text-muted">لا يوجد</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Table -->
    <div class="summary-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                السجلات المرحلة
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="summaryTable">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 8%;">
                                <i class="fas fa-hashtag me-2"></i>الرقم
                            </th>
                            <th style="width: 15%;">
                                <i class="fas fa-id-card me-2"></i>الرقم الوزاري
                            </th>
                            <th style="width: 25%;">
                                <i class="fas fa-user me-2"></i>الاسم الكامل
                            </th>
                            <th style="width: 20%;">
                                <i class="fas fa-building me-2"></i>القسم
                            </th>
                            <th style="width: 12%;">
                                <i class="fas fa-calendar me-2"></i>التاريخ
                            </th>
                            <th style="width: 10%;">
                                <i class="fas fa-calendar-times me-2"></i>عدد الأيام
                            </th>
                            <th style="width: 10%;">
                                <i class="fas fa-clock me-2"></i>تاريخ الترحيل
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in transferred_records %}
                        <tr class="transferred-row">
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <strong>{{ record.ministry_number }}</strong>
                            </td>
                            <td>
                                <strong>{{ record.employee.full_name }}</strong>
                            </td>
                            <td>{{ record.department_name }}</td>
                            <td>{{ record.date }}</td>
                            <td>
                                <span class="badge bg-warning text-dark">{{ record.days_count }} يوم</span>
                            </td>
                            <td>
                                <small class="text-muted">{{ record.transfer_date|date:"Y-m-d H:i" }}</small>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                                    <h5>لا توجد سجلات مرحلة</h5>
                                    <p>لم يتم ترحيل أي سجلات عدم صرف بعد.</p>
                                    <a href="{% url 'employment:non_payment_list' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left"></i> العودة إلى القائمة
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% if transferred_records %}
    <!-- Summary Footer -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="text-info mb-2">
                        <i class="fas fa-info-circle me-2"></i>
                        ملخص الإحصائيات
                    </h6>
                    <p class="mb-0 text-muted">
                        تم ترحيل <strong>{{ total_transferred }}</strong> سجل عدم صرف
                        بإجمالي <strong>{{ total_days }}</strong> يوم
                        {% if latest_transfer_date %}
                        - آخر ترحيل في {{ latest_transfer_date|date:"Y-m-d" }}
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set print date
    $('#printDate').text(new Date().toLocaleDateString('ar-SA'));

    // Handle print summary button (same as transfer summary)
    $('#print-summary-btn').on('click', function(e) {
        e.preventDefault();

        // Create a form to submit
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = '{% url "employment:non_payment_summary" %}';
        form.style.display = 'none';
        document.body.appendChild(form);
        form.submit();
    });
});
</script>
{% endblock %}
