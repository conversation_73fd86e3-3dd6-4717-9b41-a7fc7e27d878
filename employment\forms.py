from django import forms
from .models import Department, Position, EmploymentStatus, Employment, TechnicalPosition, EmployeePosition, EmployeeIdentification, AppointmentType, ExcessEmployee, MedicalCondition, MedicalConditionName, BtecField, BtecTeacher, EmployeeAllowance
from employees.models import Employee
import pandas as pd

class DepartmentForm(forms.ModelForm):
    class Meta:
        model = Department
        fields = ['name', 'description', 'workplace', 'school_type', 'school_gender', 'highest_grade', 'lowest_grade', 'directorate_type']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'workplace': forms.Select(attrs={'class': 'form-control', 'id': 'id_workplace'}),
            'school_type': forms.Select(attrs={'class': 'form-control', 'id': 'id_school_type'}),
            'school_gender': forms.Select(attrs={'class': 'form-control', 'id': 'id_school_gender'}),
            'highest_grade': forms.Select(attrs={'class': 'form-control', 'id': 'id_highest_grade'}),
            'lowest_grade': forms.Select(attrs={'class': 'form-control', 'id': 'id_lowest_grade'}),
            'directorate_type': forms.Select(attrs={'class': 'form-control', 'id': 'id_directorate_type'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        workplace = cleaned_data.get('workplace')

        # Clear irrelevant fields based on workplace
        if workplace == 'school':
            # Clear directorate fields
            cleaned_data['directorate_type'] = None
        elif workplace == 'directorate':
            # Clear school fields
            cleaned_data['school_type'] = None
            cleaned_data['school_gender'] = None
            cleaned_data['highest_grade'] = None
            cleaned_data['lowest_grade'] = None

        return cleaned_data

class PositionForm(forms.ModelForm):
    class Meta:
        model = Position
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }


class AppointmentTypeForm(forms.ModelForm):
    class Meta:
        model = AppointmentType
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

class EmploymentStatusForm(forms.ModelForm):
    class Meta:
        model = EmploymentStatus
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class EmploymentForm(forms.ModelForm):
    class Meta:
        model = Employment
        fields = ['employee', 'department', 'position', 'status', 'start_date', 'end_date', 'is_current']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }

class DepartmentImportForm(forms.Form):
    file = forms.FileField(
        label='ملف إكسل',
        help_text='يرجى تحميل ملف إكسل يحتوي على الأعمدة المطلوبة (راجع التعليمات أعلاه)',
        widget=forms.FileInput(attrs={'accept': '.xlsx, .xls'})
    )


class TechnicalPositionForm(forms.ModelForm):
    # Add a field for school departments that will be shown dynamically
    school_departments = forms.ModelMultipleChoiceField(
        queryset=Department.objects.filter(workplace='school'),
        required=True,
        widget=forms.SelectMultiple(attrs={'class': 'form-control select2'})
    )

    class Meta:
        model = TechnicalPosition
        fields = ['specialization', 'gender', 'vacancies', 'notes', 'school_departments']
        widgets = {
            'specialization': forms.TextInput(attrs={'class': 'form-control'}),
            'gender': forms.Select(attrs={'class': 'form-control'}),
            'vacancies': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'id': 'id_vacancies'}),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'required': 'required',
                'placeholder': 'يرجى إدخال المبرر من الشاغر'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make notes field required
        self.fields['notes'].required = True

        # Hide department field as we'll use school_departments instead
        if 'department' in self.fields:
            self.fields['department'].widget = forms.HiddenInput()
            self.fields['department'].required = False

        # Get all school departments for the dropdown
        self.fields['school_departments'].queryset = Department.objects.filter(workplace='school').order_by('name')
        self.fields['school_departments'].help_text = 'اختر الأقسام المدرسية التي توجد فيها الشواغر'

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            if not file.name.endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('يرجى تحميل ملف إكسل صالح (.xlsx أو .xls)')

            try:
                # Try to read the Excel file to validate it
                df = pd.read_excel(file)

                # Check if the file has the required columns
                required_columns = ['الاسم', 'الوصف']
                for col in required_columns:
                    if col not in df.columns:
                        raise forms.ValidationError(f'الملف يجب أن يحتوي على عمود "{col}"')

                # Reset file pointer for later use
                file.seek(0)
            except Exception as e:
                raise forms.ValidationError(f'خطأ في قراءة ملف الإكسل: {str(e)}')

        return file


class EmployeePositionForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Override the default required attribute for position field
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['position'].required = True
        self.fields['date_obtained'].required = True
        self.fields['employee'].required = False

        # Make school_level visible only for teacher positions
        if 'instance' in kwargs and kwargs['instance']:
            position = kwargs['instance'].position
            if position and position.name.lower().startswith('معلم'):
                self.fields['school_level'].widget.attrs['class'] = 'form-control'
            else:
                self.fields['school_level'].widget = forms.HiddenInput()

    class Meta:
        model = EmployeePosition
        fields = ['employee', 'position', 'date_obtained', 'school_level', 'notes']
        widgets = {
            'employee': forms.HiddenInput(attrs={'required': False}),
            'position': forms.Select(attrs={'class': 'form-control select2'}),
            'date_obtained': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'school_level': forms.Select(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')
        position = cleaned_data.get('position')
        date_obtained = cleaned_data.get('date_obtained')

        # Validate employee
        if not employee_id and not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف')

        # Validate position
        if not position:
            raise forms.ValidationError('الرجاء اختيار المسمى الوظيفي')

        # Validate date
        if not date_obtained:
            raise forms.ValidationError('الرجاء إدخال تاريخ الحصول على المسمى الوظيفي')

        # Try to get employee
        try:
            if employee_id:
                employee = Employee.objects.get(id=employee_id)
            elif ministry_number:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee_id'] = employee.id
            cleaned_data['employee'] = employee
        except Employee.DoesNotExist:
            raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        return cleaned_data


class EmployeeIdentificationForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    class Meta:
        model = EmployeeIdentification
        fields = ['employee', 'ministry_number', 'national_id', 'id_number', 'birth_day', 'birth_month', 'birth_year', 'address']
        widgets = {
            'employee': forms.HiddenInput(),
            'ministry_number': forms.TextInput(attrs={'class': 'form-control'}),
            'national_id': forms.TextInput(attrs={'class': 'form-control'}),
            'id_number': forms.TextInput(attrs={'class': 'form-control'}),
            'birth_day': forms.Select(attrs={'class': 'form-control'}),
            'birth_month': forms.Select(attrs={'class': 'form-control'}),
            'birth_year': forms.NumberInput(attrs={'class': 'form-control', 'min': '1940', 'max': '2010'}),
            'address': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['employee'].required = False

        # If we have an instance, populate the ministry_number and employee_name fields
        if 'instance' in kwargs and kwargs['instance']:
            self.fields['ministry_number'].initial = kwargs['instance'].ministry_number
            self.fields['employee_name'].initial = kwargs['instance'].employee.full_name

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')
        id_number = cleaned_data.get('id_number')

        # Validate employee - allow employees without ministry numbers
        if not employee_id and not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف أو اختيار الموظف مباشرة')

        # Try to get employee
        try:
            if employee_id:
                employee = Employee.objects.get(id=employee_id)
            elif ministry_number:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee_id'] = employee.id
            cleaned_data['employee'] = employee
        except Employee.DoesNotExist:
            raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        # Check if ID number already exists for another employee
        if id_number:
            # Exclude current instance if we're editing
            if self.instance and self.instance.pk:
                existing = EmployeeIdentification.objects.filter(id_number=id_number).exclude(pk=self.instance.pk).first()
            else:
                existing = EmployeeIdentification.objects.filter(id_number=id_number).first()

            if existing:
                raise forms.ValidationError(f'رقم الهوية {id_number} مستخدم بالفعل للموظف {existing.employee.full_name}')

        return cleaned_data


class IdNumberImportForm(forms.Form):
    """Form for importing ID numbers from Excel file"""
    excel_file = forms.FileField(
        label='ملف إكسل',
        help_text='يجب أن يحتوي الملف على عمودين: الرقم الوزاري ورقم الهوية',
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.xlsx,.xls'})
    )

    def clean_excel_file(self):
        excel_file = self.cleaned_data.get('excel_file')
        if excel_file:
            # Check file extension
            if not excel_file.name.endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('يجب أن يكون الملف بتنسيق Excel (.xlsx أو .xls)')

            # Try to read the file
            try:
                df = pd.read_excel(excel_file)

                # Check if the file has the required columns
                required_columns = ['الرقم الوزاري', 'رقم الهوية']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    raise forms.ValidationError(f'الملف لا يحتوي على الأعمدة المطلوبة: {", ".join(missing_columns)}')

                # Check if there are any empty values in required columns
                if df['الرقم الوزاري'].isnull().any():
                    raise forms.ValidationError('يوجد قيم فارغة في عمود الرقم الوزاري')

                if df['رقم الهوية'].isnull().any():
                    raise forms.ValidationError('يوجد قيم فارغة في عمود رقم الهوية')

                # Store the dataframe in the form for later use
                self.df = df

            except Exception as e:
                raise forms.ValidationError(f'حدث خطأ أثناء قراءة الملف: {str(e)}')

        return excel_file


class ExcessEmployeeSearchForm(forms.Form):
    """Form for searching excess employees"""
    search_term = forms.CharField(
        label='بحث',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'بحث بالرقم الوزاري أو الاسم'})
    )
    department = forms.ModelChoiceField(
        label='الأقسام التي بها زوائد',
        queryset=Department.objects.none(),  # Will be set dynamically in the view
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )
    status = forms.ChoiceField(
        label='الحالة',
        choices=[('', 'جميع الحالات'), ('pending', 'قيد المعالجة'), ('resolved', 'تمت المعالجة')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class ExcessEmployeeForm(forms.ModelForm):
    """Form for adding/editing excess employees"""
    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Display field for department (not saved to model, just for display)
    department_display = forms.CharField(
        label='القسم الحالي',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'department_display'})
    )

    class Meta:
        model = ExcessEmployee
        fields = ['employee', 'reason', 'status', 'resolution_notes']
        widgets = {
            'employee': forms.HiddenInput(),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'resolution_notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set required fields
        self.fields['employee'].required = True
        self.fields['reason'].required = True

        # Set non-required fields
        self.fields['status'].required = False
        self.fields['resolution_notes'].required = False

        # If we have an instance, populate the ministry_number and employee_name fields
        if 'instance' in kwargs and kwargs['instance']:
            self.fields['ministry_number'].initial = kwargs['instance'].employee.ministry_number
            self.fields['employee_name'].initial = kwargs['instance'].employee.full_name

            # Get department name
            if kwargs['instance'].current_department:
                self.fields['department_display'].initial = kwargs['instance'].current_department.name

    def clean(self):
        """Validate that the employee is not already added as excess"""
        cleaned_data = super().clean()
        employee = cleaned_data.get('employee')

        # Skip validation if we're editing an existing record
        if self.instance and self.instance.pk:
            return cleaned_data

        # Check if this employee already exists as an excess employee with status 'pending'
        if employee:
            existing_excess = ExcessEmployee.objects.filter(
                employee=employee,
                status='pending'
            ).exists()

            if existing_excess:
                raise forms.ValidationError(
                    'هذا الموظف مضاف بالفعل كموظف زائد وقيد المعالجة. لا يمكن إضافته مرة أخرى.'
                )


class ExcessEmployeeResolveForm(forms.Form):
    """Form for resolving excess employee issues"""
    resolution_notes = forms.CharField(
        label='ملاحظات المعالجة',
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=True
    )


class MedicalConditionNameForm(forms.ModelForm):
    """Form for adding/editing medical condition names"""
    class Meta:
        model = MedicalConditionName
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }


class MedicalConditionSearchForm(forms.Form):
    """Form for searching medical conditions"""
    search_term = forms.CharField(
        label='بحث',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'بحث بالرقم الوزاري أو الاسم'})
    )

    condition = forms.ModelChoiceField(
        label='الحالة المرضية',
        queryset=MedicalConditionName.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.none(),  # Will be set dynamically in the view
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    position = forms.ModelChoiceField(
        label='المسمى الوظيفي',
        queryset=Position.objects.none(),  # Will be set dynamically in the view
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    def __init__(self, *args, **kwargs):
        departments_queryset = kwargs.pop('departments_queryset', None)
        positions_queryset = kwargs.pop('positions_queryset', None)

        super().__init__(*args, **kwargs)

        if departments_queryset is not None:
            self.fields['department'].queryset = departments_queryset

        if positions_queryset is not None:
            self.fields['position'].queryset = positions_queryset


class MedicalConditionForm(forms.ModelForm):
    """Form for adding/editing medical conditions"""
    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Display field for department (not saved to model, just for display)
    department = forms.CharField(
        label='القسم',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'department_display'})
    )

    class Meta:
        model = MedicalCondition
        fields = ['employee', 'condition', 'medical_report_date', 'description', 'notes']
        widgets = {
            'employee': forms.HiddenInput(),
            'condition': forms.Select(attrs={'class': 'form-control select2'}),
            'medical_report_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set required fields
        self.fields['employee'].required = True
        self.fields['condition'].required = True
        self.fields['medical_report_date'].required = True
        self.fields['description'].required = True

        # Set non-required fields
        self.fields['notes'].required = False

        # If we have an instance, populate the ministry_number and employee_name fields
        if 'instance' in kwargs and kwargs['instance']:
            self.fields['ministry_number'].initial = kwargs['instance'].employee.ministry_number
            self.fields['employee_name'].initial = kwargs['instance'].employee.full_name

        # Populate condition choices
        self.fields['condition'].queryset = MedicalConditionName.objects.all().order_by('name')

    def clean(self):
        """Validate the form data"""
        cleaned_data = super().clean()
        employee = cleaned_data.get('employee')
        ministry_number = cleaned_data.get('ministry_number')

        # Try to get employee from ministry number if not provided
        if not employee and ministry_number:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        return cleaned_data


class BtecFieldForm(forms.ModelForm):
    """Form for adding/editing BTEC fields"""
    class Meta:
        model = BtecField
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }


class BtecTeacherForm(forms.ModelForm):
    """Form for adding/editing BTEC teachers"""
    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Display field for department (not saved to model, just for display)
    department_display = forms.CharField(
        label='القسم',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'department_display'})
    )

    # Display field for specialization (not saved to model, just for display)
    specialization_display = forms.CharField(
        label='التخصص',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'specialization_display'})
    )

    class Meta:
        model = BtecTeacher
        fields = ['employee', 'field']
        widgets = {
            'employee': forms.HiddenInput(),
            'field': forms.Select(attrs={'class': 'form-control select2'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set required fields
        self.fields['employee'].required = True
        self.fields['field'].required = True

        # If we have an instance, populate the display fields
        if 'instance' in kwargs and kwargs['instance']:
            instance = kwargs['instance']
            self.fields['ministry_number'].initial = instance.ministry_number
            self.fields['employee_name'].initial = instance.employee.full_name
            self.fields['department_display'].initial = instance.department_name
            self.fields['specialization_display'].initial = instance.specialization

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        field = cleaned_data.get('field')

        # Validate employee
        if not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف')

        # Try to get employee by ministry number
        try:
            from employees.models import Employee
            employee = Employee.objects.get(identifications__ministry_number=ministry_number)
            cleaned_data['employee'] = employee
        except Employee.DoesNotExist:
            raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        # Validate field
        if not field:
            raise forms.ValidationError('الرجاء اختيار الحقل')

        # Check if this combination already exists (for new records)
        if not self.instance.pk:
            existing = BtecTeacher.objects.filter(employee=employee, field=field).exists()
            if existing:
                raise forms.ValidationError(f'الموظف {employee.full_name} مضاف بالفعل في حقل {field.name}')

        return cleaned_data



class BtecSearchForm(forms.Form):
    """Form for searching BTEC teachers"""
    search_term = forms.CharField(
        label='بحث',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'بحث بالرقم الوزاري أو الاسم'})
    )

    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.none(),  # Will be set dynamically in the view
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    field = forms.ModelChoiceField(
        label='الحقل',
        queryset=BtecField.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    specialization = forms.ChoiceField(
        label='التخصص',
        choices=[],  # Will be set dynamically in the view
        required=False,
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    def __init__(self, *args, **kwargs):
        departments_queryset = kwargs.pop('departments_queryset', None)
        specializations_choices = kwargs.pop('specializations_choices', None)
        super().__init__(*args, **kwargs)

        if departments_queryset is not None:
            self.fields['department'].queryset = departments_queryset

        if specializations_choices is not None:
            # Add empty choice at the beginning
            choices = [('', '---------')] + [(choice, choice) for choice in specializations_choices]
            self.fields['specialization'].choices = choices


class EmployeeAllowanceSearchForm(forms.Form):
    """Form for searching employee allowances"""
    search_term = forms.CharField(
        label='بحث',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'بحث بالرقم الوزاري أو الاسم'})
    )

    education_allowance = forms.ChoiceField(
        label='علاوة التعليم',
        choices=[('', 'الكل'), ('yes', 'نعم'), ('no', 'لا')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    adjustment_allowance = forms.ChoiceField(
        label='التجيير',
        choices=[('', 'الكل'), ('yes', 'نعم'), ('no', 'لا')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    transportation_allowance = forms.ChoiceField(
        label='التنقلات',
        choices=[('', 'الكل'), ('yes', 'نعم'), ('no', 'لا')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    supervisory_allowance = forms.ChoiceField(
        label='العلاوة الإشرافية',
        choices=[('', 'الكل'), ('yes', 'نعم'), ('no', 'لا')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    technical_allowance = forms.ChoiceField(
        label='علاوة فنية',
        choices=[('', 'الكل'), ('yes', 'نعم'), ('no', 'لا')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class EmployeeAllowanceForm(forms.ModelForm):
    """Form for adding/editing employee allowances"""
    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    class Meta:
        model = EmployeeAllowance
        fields = ['employee', 'education_allowance', 'adjustment_allowance', 'transportation_allowance', 'supervisory_allowance', 'technical_allowance']
        widgets = {
            'employee': forms.HiddenInput(),
            'education_allowance': forms.Select(attrs={'class': 'form-control'}),
            'adjustment_allowance': forms.Select(attrs={'class': 'form-control'}),
            'transportation_allowance': forms.Select(attrs={'class': 'form-control'}),
            'supervisory_allowance': forms.Select(attrs={'class': 'form-control'}),
            'technical_allowance': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set required fields
        self.fields['employee'].required = True

        # If we have an instance, populate the ministry_number and employee_name fields
        if 'instance' in kwargs and kwargs['instance']:
            self.fields['ministry_number'].initial = kwargs['instance'].ministry_number
            self.fields['employee_name'].initial = kwargs['instance'].employee.full_name

    def clean(self):
        """Validate that the employee exists"""
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee = cleaned_data.get('employee')

        # If no employee is set, try to find by ministry number
        if not employee and ministry_number:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        # Check if allowance record already exists for this employee (only for new records)
        if employee and not self.instance.pk:
            existing_allowance = EmployeeAllowance.objects.filter(employee=employee).exists()
            if existing_allowance:
                raise forms.ValidationError('يوجد بالفعل سجل علاوات لهذا الموظف. يمكنك تعديل السجل الموجود.')

        return cleaned_data

