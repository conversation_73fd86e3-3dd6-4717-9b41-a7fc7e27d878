from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.conf import settings

class Notification(models.Model):
    """Model for system notifications"""

    NOTIFICATION_TYPES = [
        ('info', 'معلومات'),
        ('success', 'نجاح'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
    ]

    ICON_CHOICES = [
        ('fa-user-plus', 'إضافة مستخدم'),
        ('fa-check-circle', 'تم بنجاح'),
        ('fa-exclamation-triangle', 'تحذير'),
        ('fa-times-circle', 'خطأ'),
        ('fa-info-circle', 'معلومات'),
        ('fa-bell', 'إشعار عام'),
        ('fa-file-alt', 'ملف'),
        ('fa-calendar', 'تاريخ'),
        ('fa-user', 'مستخدم'),
        ('fa-cog', 'إعدادات'),
    ]

    title = models.CharField(_('العنوان'), max_length=200)
    message = models.TextField(_('الرسالة'))
    notification_type = models.CharField(
        _('نوع الإشعار'),
        max_length=20,
        choices=NOTIFICATION_TYPES,
        default='info'
    )
    icon = models.CharField(
        _('الأيقونة'),
        max_length=50,
        choices=ICON_CHOICES,
        default='fa-bell'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name=_('المستخدم'),
        null=True,
        blank=True,
        help_text=_('إذا تُرك فارغاً، سيظهر الإشعار لجميع المستخدمين')
    )
    is_read = models.BooleanField(_('مقروء'), default=False)
    is_global = models.BooleanField(_('إشعار عام'), default=False)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    read_at = models.DateTimeField(_('تاريخ القراءة'), null=True, blank=True)

    class Meta:
        verbose_name = _('إشعار')
        verbose_name_plural = _('الإشعارات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.user.username if self.user else 'عام'}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()

    def get_time_since_created(self):
        """Get human readable time since creation"""
        now = timezone.now()
        diff = now - self.created_at

        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "منذ لحظات"

    def get_background_color(self):
        """Get background color based on notification type"""
        colors = {
            'info': 'bg-primary',
            'success': 'bg-success',
            'warning': 'bg-warning',
            'error': 'bg-danger',
        }
        return colors.get(self.notification_type, 'bg-primary')

    @classmethod
    def create_notification(cls, title, message, notification_type='info', icon='fa-bell', user=None, is_global=False):
        """Create a new notification"""
        return cls.objects.create(
            title=title,
            message=message,
            notification_type=notification_type,
            icon=icon,
            user=user,
            is_global=is_global
        )

    @classmethod
    def get_user_notifications(cls, user, limit=10):
        """Get notifications for a specific user"""
        return cls.objects.filter(
            models.Q(user=user) | models.Q(is_global=True)
        ).order_by('-created_at')[:limit]

    @classmethod
    def get_unread_count(cls, user):
        """Get count of unread notifications for a user"""
        return cls.objects.filter(
            models.Q(user=user) | models.Q(is_global=True),
            is_read=False
        ).count()
